请创建一个基于P5.js的全屏互动点阵动画生成器，满足以下技术需求：
## 核心功能
- 使用P5.js实现全屏点阵动画，动画需覆盖整个视口区域
- 点阵矩阵总面积必须至少是可见区域的10倍，确保即使在最小网格间距下也能完全覆盖
- 提供多种动画模式：波浪(Wave)、脉冲(Pulse)、涟漪(Ripple)、噪声(Noise)
- 支持多种点形状选择：圆形、方形、十字、三角形、菱形等
- 所有控制面板放置在页面右侧，移动设备下可折叠至底部
## 可调节参数
- 点阵密度：控制每行/每列点的数量
- 形状大小：调整点的尺寸
- 动画速度：控制动画效果的速度和幅度
- 网格间距：调整点与点之间的距离
## 技术规范
- 使用HTML5、TailwindCSS 3.0+（通过CDN引入）和P5.js
- 实现完整的深色/浅色模式切换功能，默认跟随系统设置
- 代码需包含性能优化逻辑，仅渲染可见区域内及边缘附近的点
- 动画必须流畅运行，无卡顿
## 响应式设计
- 页面必须在所有设备上（手机、平板、桌面）完美展示
- 移动端视图中，控制面板应可折叠/展开
- 针对不同屏幕尺寸优化布局和字体大小
- 确保移动端有良好的触控体验
## 界面要素
- 动画模式选择器（波浪、脉冲、涟漪、噪声）
- 形状选择器（带图标展示各种形状）
- 滑块控制：密度、大小、速度、间距
- 主题切换按钮
- 显示矩阵覆盖信息和总点数