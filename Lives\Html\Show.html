<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI发展史 - 推理模型的实现原理</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Oswald:wght@700&family=Roboto+Condensed:wght@700&family=Noto+Sans+SC:wght@400;700;900&display=swap');
        
        body {
            background-color: #111;
            color: #fff;
            font-family: 'Noto Sans SC', sans-serif;
            overflow-x: hidden;
        }
        
        .card-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #0a0a0a 0%, #222 100%);
        }
        
        .title-block {
            font-family: '<PERSON>', sans-serif;
            line-height: 0.9;
            letter-spacing: -2px;
        }
        
        .accent-text {
            font-family: 'Roboto Condensed', sans-serif;
            letter-spacing: 1px;
        }
        
        .diagonal-line {
            position: absolute;
            height: 3px;
            width: 100%;
            background-color: #ff3e00;
            transform: rotate(-5deg);
            z-index: 10;
        }
        
        .clip-path-element {
            clip-path: polygon(0% 0%, 100% 15%, 100% 100%, 0% 85%);
        }
        
        .circle-element {
            width: 250px;
            height: 250px;
            border-radius: 50%;
            position: absolute;
            background: rgba(255, 62, 0, 0.2);
            z-index: -1;
        }
        
        .comparison-table {
            border-collapse: separate;
            border-spacing: 0 10px;
        }
        
        .comparison-table th, .comparison-table td {
            padding: 15px;
            text-align: left;
        }
        
        .comparison-table tr:nth-child(odd) {
            background-color: rgba(255, 255, 255, 0.05);
        }
        
        .highlight-area {
            position: relative;
            overflow: hidden;
        }
        
        .highlight-area::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 62, 0, 0.3) 0%, rgba(0, 0, 0, 0) 70%);
            z-index: -1;
        }
        
        .floating-text {
            position: absolute;
            opacity: 0.1;
            font-size: 120px;
            font-weight: 900;
            z-index: -1;
            white-space: nowrap;
            transform: rotate(-90deg);
        }
        
        .animated-border {
            position: relative;
        }
        
        .animated-border::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 3px;
            background-color: #00d9ff;
            transition: width 1.5s ease;
        }
        
        .animated-border:hover::after {
            width: 100%;
        }
        
        .text-shadow {
            text-shadow: 3px 3px 0px rgba(255, 62, 0, 0.7);
        }
        
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-15px); }
            100% { transform: translateY(0px); }
        }
        
        .float-animation {
            animation: float 6s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <div class="card-container relative">
        <!-- 背景装饰元素 -->
        <div class="circle-element" style="top: 10%; left: 5%;"></div>
        <div class="circle-element" style="bottom: 20%; right: 10%; background: rgba(0, 217, 255, 0.2);"></div>
        <div class="floating-text" style="top: 30%; left: -10%;">INTELLIGENCE</div>
        <div class="floating-text" style="bottom: 20%; right: -50%;">ARTIFICIELLE</div>
        
        <div class="diagonal-line" style="top: 120px;"></div>
        <div class="diagonal-line" style="bottom: 150px;"></div>
        
        <!-- 主要内容 -->
        <div class="container mx-auto px-6 py-12">
            <!-- 标题区 -->
            <div class="mb-16 relative">
                <span class="accent-text text-sm tracking-widest text-gray-400 uppercase">AI EVOLUTION SERIES</span>
                <h1 class="title-block text-7xl md:text-9xl font-black mb-2 text-white text-shadow">
                    <span class="text-yellow-400">推理</span>模型
                </h1>
                <h2 class="title-block text-5xl font-bold text-gray-200 tracking-tighter">
                    DeepSeek <span class="text-cyan-400">R1</span> 的实现原理
                </h2>
                <p class="text-xl text-gray-400 mt-6 max-w-3xl">
                    当"预测下一个词"的能力演变成能够分步骤解决复杂问题的"推理"
                </p>
            </div>
            
            <!-- 内容卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-10">
                <!-- 左侧主要解释卡片 -->
                <div class="md:col-span-2 bg-gray-900 p-8 clip-path-element highlight-area">
                    <h3 class="text-3xl font-bold mb-6 animated-border inline-block">深入解析推理模型</h3>
                    <p class="mb-4">
                        目前所谓的"推理"能力，和人类严谨的逻辑演绎有本质区别。AI的"推理"更像是基于海量数据学习到的、极其复杂的模式识别和序列生成能力。
                    </p>
                    <div class="mb-8">
                        <h4 class="text-xl font-bold mb-3 text-yellow-400">超大规模和高质量的训练数据</h4>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>
                                <span class="font-bold">基础：</span>强大的基座模型，用万亿级别token进行预训练
                            </li>
                            <li>
                                <span class="font-bold">强化：</span>特意"喂"给它巨量的、高质量的、蕴含逻辑关系和推理过程的数据
                            </li>
                            <li class="pl-5">
                                - 数学题库、代码数据、科学文献、逻辑谜题
                            </li>
                            <li class="pl-5 text-cyan-400 font-bold">
                                - 思维链(Chain-of-Thought)数据：重中之重！
                            </li>
                        </ul>
                    </div>
                    
                    <div class="mb-8">
                        <h4 class="text-xl font-bold mb-3 text-yellow-400">特殊的训练/微调技术</h4>
                        <p class="italic text-gray-300 mb-3">这是将"潜力"转化为"能力"的关键步骤</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>
                                <span class="font-bold">指令微调：</span>理解和遵循人类指令的能力训练
                            </li>
                            <li>
                                <span class="font-bold">强化学习与人类反馈(RLHF/DPO)：</span>提升推理"可靠性"的关键
                            </li>
                            <li>
                                <span class="font-bold">过程监督：</span>评价推理的每一步是否合理、符合逻辑
                            </li>
                            <li>
                                <span class="font-bold">思维链微调：</span>强制模型学习生成中间步骤的模式
                            </li>
                        </ul>
                    </div>
                    
                    <div class="mt-6 text-right">
                        <span class="text-xs text-gray-500 uppercase accent-text tracking-widest">INTELLIGENCE ARTICLE</span>
                    </div>
                </div>
                
                <!-- 右侧辅助卡片 -->
                <div class="space-y-6">
                    <!-- 核心思路卡片 -->
                    <div class="bg-gradient-to-br from-yellow-900 to-yellow-700 p-6 highlight-area float-animation">
                        <h3 class="text-2xl font-bold mb-4">R1实现原理的核心思路</h3>
                        <p class="text-white">
                            用一个超强的基座模型，给它"猛灌"包含大量逻辑、数学、代码、思维链的高质量数据，
                            并辅以强调"过程正确性"的精细微调技术，最终"逼"它学会在解决复杂问题时，
                            生成类似人类的、循序渐进的思考步骤。
                        </p>
                    </div>
                    
                    <!-- 模型架构卡片 -->
                    <div class="bg-gray-900 p-6">
                        <h3 class="text-xl font-bold mb-4 text-cyan-400">
                            <i class="fas fa-microchip mr-2"></i>模型架构优化
                        </h3>
                        <p class="mb-3">
                            虽然基于Transformer架构，但可能有变体或优化：
                        </p>
                        <ul class="list-disc pl-5">
                            <li>Mixture-of-Experts (MoE)架构</li>
                            <li>在保持计算效率的同时增加模型参数量</li>
                            <li>理论上能提升模型容量和处理复杂任务的能力</li>
                        </ul>
                    </div>
                    
                    <!-- 装饰性引用卡片 -->
                    <div class="bg-cyan-900 p-6 transform -rotate-3">
                        <p class="text-lg italic">
                            "人工智能的进步不在于它能做什么，而在于它如何思考。"
                        </p>
                        <p class="text-right text-sm mt-2">— L'AVENIR DE L'INTELLIGENCE</p>
                    </div>
                </div>
            </div>
            
            <!-- 比较表格区域 -->
            <div class="mt-16 relative overflow-hidden">
                <h3 class="text-3xl font-bold mb-8 animated-border inline-block">与普通基座模型的区别</h3>
                <p class="mb-6 text-gray-300">
                    别把基座模型和经过特殊优化的推理模型混为一谈，这就像拿一个刚学会走路的婴儿和奥运短跑冠军比速度一样，有点欺负人了。
                </p>
                
                <div class="overflow-x-auto">
                    <table class="comparison-table w-full">
                        <thead>
                            <tr class="bg-gradient-to-r from-red-800 to-red-600">
                                <th class="text-lg">特征</th>
                                <th class="text-lg">普通基座模型 (Base Model)</th>
                                <th class="text-lg">推理优化模型 (e.g., DeepSeek R1)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="font-bold">训练目标</td>
                                <td>预测下一个词/token，学习广泛的语言模式和知识</td>
                                <td>优化解决需要多步逻辑、计算或规划的复杂任务</td>
                            </tr>
                            <tr>
                                <td class="font-bold">训练数据</td>
                                <td>通用、海量的互联网文本和代码</td>
                                <td>侧重数学、代码、逻辑题、科学文献、思维链数据</td>
                            </tr>
                            <tr>
                                <td class="font-bold">核心能力</td>
                                <td>语言生成流畅、知识覆盖广、文本理解</td>
                                <td>数学推理、代码生成与理解、逻辑分析、复杂指令遵循</td>
                            </tr>
                            <tr>
                                <td class="font-bold">微调阶段</td>
                                <td>通常未经过特定任务的大规模指令微调或RLHF</td>
                                <td>经过大量指令微调，特别是针对推理任务的过程/结果监督RLHF/DPO</td>
                            </tr>
                            <tr>
                                <td class="font-bold">输出特点</td>
                                <td>可能直接给答案，不一定有中间步骤；
                                    <td>可能直接给答案，不一定有中间步骤；有时会"一本正经地胡说八道"</td>
                                    <td>倾向于生成详细的、分步骤的推理过程(CoT)，答案准确率更高（在擅长领域）</td>
                                </tr>
                                <tr>
                                    <td class="font-bold">类比</td>
                                    <td>一个博览群书但未经专业训练的"通才"</td>
                                    <td>一个在特定领域（如数理逻辑）深造过的"专才/学霸"</td>
                                </tr>
                                <tr>
                                    <td class="font-bold">弱点</td>
                                    <td>复杂推理任务容易出错，遵循复杂指令能力较弱</td>
                                    <td>在通用闲聊、创意写作等方面可能不如专门优化的对话模型；仍可能在不擅长的领域犯错</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- 底部装饰区 -->
                <div class="mt-20 mb-10 flex flex-col md:flex-row justify-between items-center">
                    <div>
                        <span class="text-xs text-gray-500 uppercase tracking-widest">AI DEVELOPMENT HISTORY</span>
                        <h4 class="text-2xl font-bold">AI发展史 | 推理模型专题</h4>
                    </div>
                    
                    <div class="flex space-x-4 mt-4 md:mt-0">
                        <div class="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-brain text-2xl"></i>
                        </div>
                        <div class="w-16 h-16 bg-cyan-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-code-branch text-2xl"></i>
                        </div>
                        <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-lightbulb text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            // 简单的滚动效果
            document.addEventListener('DOMContentLoaded', function() {
                const elements = document.querySelectorAll('.highlight-area');
                
                function checkScroll() {
                    elements.forEach(el => {
                        const rect = el.getBoundingClientRect();
                        const isInView = (rect.top <= window.innerHeight * 0.8 && rect.bottom >= 0);
                        
                        if (isInView) {
                            el.classList.add('opacity-100');
                            el.classList.remove('opacity-70');
                        } else {
                            el.classList.add('opacity-70');
                            el.classList.remove('opacity-100');
                        }
                    });
                }
                
                window.addEventListener('scroll', checkScroll);
                checkScroll(); // 初始检查
            });
        </script>
    </body>
    </html>  