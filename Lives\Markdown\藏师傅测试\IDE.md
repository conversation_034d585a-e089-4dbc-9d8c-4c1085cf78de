请创建一个基于纯 HTML 的 Markdown 笔记编辑器，具体要求如下：

## 功能需求
1. **编辑器布局**
   - 左右分屏设计：左侧 Markdown 输入，右侧实时预览
   - 可拖拽调整分屏比例
   - 工具栏快捷按钮（加粗、斜体、标题、链接、图片、代码块等）
   - 支持键盘快捷键操作

2. **笔记管理**
   - 侧边栏显示所有笔记列表
   - 笔记搜索和筛选功能
   - 支持标签分类系统
   - 创建、删除、重命名笔记

3. **数据功能**
   - 使用 localStorage 保存所有笔记
   - 自动保存（显示保存状态）
   - 导出为 .md/.txt/.html 文件
   - 导入 Markdown 文件
   - 字数统计和预计阅读时间

## 技术要求
- 使用 marked.js 或类似库进行 Markdown 解析
- 使用 Prism.js 实现代码高亮
- TailwindCSS 3.0+ 样式框架
- 图标使用 Lucide Icons 或 Heroicons
- 实现深色/浅色主题切换
- 响应式设计，移动端友好

## 交互细节
- 实时预览（输入时延迟渲染）
- 快捷键提示浮层
- 搜索结果高亮显示
- 拖拽上传图片（转 base64）
- 打印优化样式