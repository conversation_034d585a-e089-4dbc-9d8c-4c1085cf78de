核心目标：

请将以下数据转换成一个交互式可视化图表。设计要求视觉美观且易于理解，整个实现代码需包含在单个HTML文件中（集成所需的CSS和JavaScript）。添加适当的交互功能，以增强用户体验和数据分析价值。
数据是各个城市每月自动驾驶车辆测试次数
城市,1月,2月,3月,4月,5月,6月,7月,8月,9月,10月,11月,12月,累计测试里程(公里),平均单次测试时长(小时)
北京,150,130,165,175,180,190,205,200,190,180,195,210,15000,2.5
上海,180,160,200,210,220,230,250,245,230,220,240,255,18000,2.8
广州,120,105,135,145,155,160,170,165,155,145,160,175,12500,2.2
深圳,110,95,125,135,140,150,160,155,145,135,150,165,11000,2.0
成都,90,80,100,110,115,120,130,125,115,105,120,130,9000,1.8
杭州,80,70,90,95,100,105,115,110,100,90,105,115,8000,1.9
武汉,70,60,80,85,90,95,100,95,90,80,95,100,7000,1.7
重庆,65,55,75,80,85,90,95,90,85,75,90,95,6500,1.6

创建中文交互式数据看板，展示中国主要城市自动驾驶车辆月度测试数据
包含四种互补的图表类型：趋势折线图、对比柱状图、时长柱状图和月度热力图
强调城市间的横向比较和月度变化模式

UI/UX要求：
纯中文界面（包括轴标签、图例、提示等）
响应式布局（适应不同屏幕）
现代简洁风格（蓝白主色调）
视觉层次分明（标题>图表>控制元素）
包含数据看板说明文字

技术约束：
单HTML文件实现（内联CSS/JS）
使用D3.js v7库
无后端依赖
兼容主流现代浏览器

特色功能：
多视图协同过滤（选择城市/月份自动同步所有图表）
智能颜色映射（自动适应数据范围）
动态轴标签（根据视图切换自动更新描述）
数据强调策略（高亮元素的宽度/透明度变化）

附加说明：
热力图需要处理数值标签的可读性（自动判断显示位置）
折线图需显示两种交互状态（常态/高亮）
柱状图要考虑长城市名的旋转显示

所有交互需考虑移动端触摸事件