body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    min-height: 100vh;
    background-color: #f0f0f0;
    color: #333;
    margin: 0;
}

.instructions {
    text-align: center;
    margin-bottom: 2rem;
}

.scene {
    width: 200px;
    height: 200px;
    perspective: 600px;
    margin: 40px 0;
}

.cube {
    width: 100%;
    height: 100%;
    position: relative;
    transform-style: preserve-3d;
    transform: translateZ(-100px) rotateX(-30deg) rotateY(-45deg);
    transition: transform 1s ease-in-out;
}

.face {
    position: absolute;
    width: 200px;
    height: 200px;
    background-color: rgba(0, 123, 255, 0.7);
    border: 2px solid #fff;
    color: white;
    font-size: 24px;
    font-weight: bold;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: transform 1.2s ease-in-out;
    backface-visibility: hidden; /* Hide the back of the face when rotated */
}

/* Initial Folded State */
.front  { transform: rotateY(  0deg) translateZ(100px); }
.back   { transform: rotateY(180deg) translateZ(100px); }
.right  { transform: rotateY( 90deg) translateZ(100px); }
.left   { transform: rotateY(-90deg) translateZ(100px); }
.top    { transform: rotateX( 90deg) translateZ(100px); }
.bottom { transform: rotateX(-90deg) translateZ(100px); }

/* Unfolded State - triggered by adding the 'unfolded' class with JS */
.front.unfolded  { transform: rotateY(0) translateZ(0); }
.top.unfolded    { transform: translateY(-200px) rotateX(0); }
.bottom.unfolded { transform: translateY(200px) rotateX(0); }
.left.unfolded   { transform: translateX(-200px) rotateY(0); }
.right.unfolded  { transform: translateX(200px) rotateY(0); }
.back.unfolded   { transform: translateX(400px) rotateY(0); }


.reset-button {
    margin-top: 2rem;
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
    border: 1px solid #007bff;
    background-color: #007bff;
    color: white;
    border-radius: 5px;
    transition: background-color 0.3s, transform 0.2s;
}

.reset-button:hover {
    background-color: #0056b3;
}

.reset-button:active {
    transform: scale(0.95);
}
