<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>像说话一样写作101</title>
    <!-- TailwindCSS 通过CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        // 配置Tailwind
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                    },
                },
            },
        }
    </script>
    <style type="text/css">
        /* 自定义样式 */
        .tab-content {
            display: none;
            animation: fadeIn 0.5s ease-in-out;
        }
        .tab-content.active {
            display: block;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        /* 卡片悬停效果 */
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px) scale(1.01);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        /* 按钮悬停效果 */
        .btn-hover {
            transition: all 0.2s ease;
        }
        .btn-hover:hover {
            transform: translateY(-2px) scale(1.05);
        }
        /* 平滑滚动 */
        html {
            scroll-behavior: smooth;
        }
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        .dark ::-webkit-scrollbar-track {
            background: #1a1a1a;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100 transition-colors duration-300">
    <!-- 导航栏 -->
    <nav class="sticky top-0 z-50 bg-white dark:bg-gray-800 shadow-md">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <span class="text-xl font-bold text-primary-600 dark:text-primary-400">像说话一样写作101</span>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- 深色模式切换按钮 -->
                    <button id="theme-toggle" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none">
                        <i class="fas fa-sun text-yellow-500 dark:hidden"></i>
                        <i class="fas fa-moon text-blue-300 hidden dark:block"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 标签页导航 -->
        <div class="mb-8 border-b border-gray-200 dark:border-gray-700">
            <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" id="tabs">
                <li class="mr-2">
                    <a href="#tab1" class="tab-link inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-primary-600 hover:border-primary-300 dark:hover:text-primary-300 active" data-tab="tab1">
                        <i class="fas fa-book mr-2"></i>写作基础
                    </a>
                </li>
                <li class="mr-2">
                    <a href="#tab2" class="tab-link inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-primary-600 hover:border-primary-300 dark:hover:text-primary-300" data-tab="tab2">
                        <i class="fas fa-pen-fancy mr-2"></i>表达技巧
                    </a>
                </li>
                <li class="mr-2">
                    <a href="#tab3" class="tab-link inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-primary-600 hover:border-primary-300 dark:hover:text-primary-300" data-tab="tab3">
                        <i class="fas fa-brain mr-2"></i>思维方法
                    </a>
                </li>
                <li class="mr-2">
                    <a href="#tab4" class="tab-link inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-primary-600 hover:border-primary-300 dark:hover:text-primary-300" data-tab="tab4">
                        <i class="fas fa-graduation-cap mr-2"></i>实践练习
                    </a>
                </li>
            </ul>
        </div>

        <!-- 标签页内容 -->
        <div class="tab-content-container">
            <!-- 标签页1：写作基础 -->
            <div id="tab1" class="tab-content active">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 卡片1 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">写作的本质</h2>
                        <p class="mb-4">写作的本质是表达思想和沟通。像说话一样写作，意味着保持自然、真实和直接。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>写作不是为了炫技，而是为了传递信息</li>
                            <li>好的写作应该像流畅的对话一样自然</li>
                            <li>写作的目的是让读者理解，而非让他们惊叹于你的词汇量</li>
                        </ul>
                    </div>
                    
                    <!-- 卡片2 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">清晰的表达</h2>
                        <p class="mb-4">清晰是写作的首要目标。复杂的想法需要简单的语言来表达。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>使用简洁明了的语言</li>
                            <li>避免不必要的修饰和华丽辞藻</li>
                            <li>一个段落表达一个核心观点</li>
                            <li>使用具体的例子来解释抽象的概念</li>
                        </ul>
                    </div>
                    
                    <!-- 卡片3 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">写作的节奏</h2>
                        <p class="mb-4">好的写作有节奏感，就像好的对话一样。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>交替使用长句和短句</li>
                            <li>段落长度要有变化</li>
                            <li>使用转折词来引导读者的思路</li>
                            <li>适当使用停顿和空白</li>
                        </ul>
                    </div>
                    
                    <!-- 卡片4 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">精简与删减</h2>
                        <p class="mb-4">好的写作往往来自于删减而非添加。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>删除多余的形容词和副词</li>
                            <li>避免重复表达同一个意思</li>
                            <li>一句话只表达一个想法</li>
                            <li>写完初稿后，尝试减少20%的字数</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 标签页3：思维方法 -->
            <div id="tab3" class="tab-content">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 卡片1 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">以读者为中心</h2>
                        <p class="mb-4">好的写作总是考虑读者的需求和理解能力。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>了解你的目标读者是谁</li>
                            <li>预测读者可能的问题和疑惑</li>
                            <li>从读者的角度思考，什么对他们有价值</li>
                            <li>使用读者熟悉的例子和参考</li>
                        </ul>
                    </div>
                    
                    <!-- 卡片2 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">结构化思考</h2>
                        <p class="mb-4">清晰的结构是有效写作的基础。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>在开始写作前，先列出主要观点</li>
                            <li>确保每个段落都有明确的主题句</li>
                            <li>使用逻辑顺序组织内容（时间顺序、重要性顺序等）</li>
                            <li>使用小标题和分段来增强可读性</li>
                        </ul>
                    </div>
                    
                    <!-- 卡片3 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">批判性思考</h2>
                        <p class="mb-4">好的写作反映了深入的思考过程。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>质疑自己的假设和前提</li>
                            <li>考虑多个角度和观点</li>
                            <li>寻找证据支持你的论点</li>
                            <li>承认复杂性和不确定性</li>
                        </ul>
                    </div>
                    
                    <!-- 卡片4 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">创造性思维</h2>
                        <p class="mb-4">创造性思维可以让你的写作更加独特和有吸引力。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>尝试不同的角度和视角</li>
                            <li>建立意想不到的联系</li>
                            <li>挑战常规思维和假设</li>
                            <li>使用类比和隐喻来解释复杂概念</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 标签页4：实践练习 -->
            <div id="tab4" class="tab-content">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 卡片1 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">日常写作练习</h2>
                        <p class="mb-4">写作是一种需要不断练习的技能。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>每天写作15-30分钟，不管内容如何</li>
                            <li>保持写作日记，记录日常观察和思考</li>
                            <li>尝试不同类型的写作（故事、随笔、信件等）</li>
                            <li>写下你对日常事物的观察和感受</li>
                        </ul>
                    </div>
                    
                    <!-- 卡片2 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">阅读与分析</h2>
                        <p class="mb-4">阅读是提高写作能力的重要途径。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>广泛阅读不同类型的文章和书籍</li>
                            <li>分析你喜欢的作者的写作风格和技巧</li>
                            <li>注意作者如何开始和结束一个段落</li>
                            <li>收集好的句子和表达方式，理解为什么它们有效</li>
                        </ul>
                    </div>
                    
                    <!-- 卡片3 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">修改与编辑</h2>
                        <p class="mb-4">好的写作往往来自于反复修改。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>写完初稿后，放置一段时间再回来修改</li>
                            <li>大声朗读你的文章，找出不自然的部分</li>
                            <li>请他人阅读并提供反馈</li>
                            <li>关注一次只修改一个方面（内容、结构、语法等）</li>
                        </ul>
                    </div>
                    
                    <!-- 卡片4 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">写作的声音</h2>
                        <p class="mb-4">每个人的写作都应该有自己独特的声音，这是你个性的体现。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>写作时保持真实，不要模仿他人的风格</li>
                            <li>使用你日常说话的语气和词汇</li>
                            <li>不要害怕展示你的个性和观点</li>
                            <li>随着时间的推移，你的写作声音会自然形成</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 标签页2：表达技巧 -->
            <div id="tab2" class="tab-content">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 卡片1 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">使用日常语言</h2>
                        <p class="mb-4">像说话一样写作的核心是使用日常语言，避免过于学术化或正式的表达。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>选择简单、常用的词汇</li>
                            <li>避免使用行业术语，除非必要</li>
                            <li>如果一个简单的词能表达清楚，就不要用复杂的词</li>
                            <li>写完后大声朗读，检查是否自然流畅</li>
                        </ul>
                    </div>
                    
                    <!-- 卡片2 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">讲故事的力量</h2>
                        <p class="mb-4">人类天生喜欢故事，在写作中融入故事元素能让内容更有吸引力。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>用个人经历或案例开始一个话题</li>
                            <li>创造情境和场景，让读者身临其境</li>
                            <li>使用对话来展示不同的观点</li>
                            <li>保持故事的连贯性和逻辑性</li>
                        </ul>
                    </div>
                    
                    <!-- 卡片3 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">修辞手法的运用</h2>
                        <p class="mb-4">适当的修辞手法可以让你的写作更加生动有力。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>比喻：将抽象概念与具体事物联系起来</li>
                            <li>对比：通过对比突出特点</li>
                            <li>排比：强调重点，增强节奏感</li>
                            <li>反问：引发读者思考</li>
                        </ul>
                    </div>
                    
                    <!-- 卡片4 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">精简与删减</h2>
                        <p class="mb-4">好的写作往往来自于删减而非添加。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>删除多余的形容词和副词</li>
                            <li>避免重复表达同一个意思</li>
                            <li>一句话只表达一个想法</li>
                            <li>写完初稿后，尝试减少20%的字数</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 标签页3：思维方法 -->
            <div id="tab3" class="tab-content">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 卡片1 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">以读者为中心</h2>
                        <p class="mb-4">好的写作总是考虑读者的需求和理解能力。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>了解你的目标读者是谁</li>
                            <li>预测读者可能的问题和疑惑</li>
                            <li>从读者的角度思考，什么对他们有价值</li>
                            <li>使用读者熟悉的例子和参考</li>
                        </ul>
                    </div>
                    
                    <!-- 卡片2 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">结构化思考</h2>
                        <p class="mb-4">清晰的结构是有效写作的基础。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>在开始写作前，先列出主要观点</li>
                            <li>确保每个段落都有明确的主题句</li>
                            <li>使用逻辑顺序组织内容（时间顺序、重要性顺序等）</li>
                            <li>使用小标题和分段来增强可读性</li>
                        </ul>
                    </div>
                    
                    <!-- 卡片3 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">批判性思考</h2>
                        <p class="mb-4">好的写作反映了深入的思考过程。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>质疑自己的假设和前提</li>
                            <li>考虑多个角度和观点</li>
                            <li>寻找证据支持你的论点</li>
                            <li>承认复杂性和不确定性</li>
                        </ul>
                    </div>
                    
                    <!-- 卡片4 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">创造性思维</h2>
                        <p class="mb-4">创造性思维可以让你的写作更加独特和有吸引力。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>尝试不同的角度和视角</li>
                            <li>建立意想不到的联系</li>
                            <li>挑战常规思维和假设</li>
                            <li>使用类比和隐喻来解释复杂概念</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 标签页4：实践练习 -->
            <div id="tab4" class="tab-content">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 卡片1 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">日常写作练习</h2>
                        <p class="mb-4">写作是一种需要不断练习的技能。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>每天写作15-30分钟，不管内容如何</li>
                            <li>保持写作日记，记录日常观察和思考</li>
                            <li>尝试不同类型的写作（故事、随笔、信件等）</li>
                            <li>写下你对日常事物的观察和感受</li>
                        </ul>
                    </div>
                    
                    <!-- 卡片2 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">阅读与分析</h2>
                        <p class="mb-4">阅读是提高写作能力的重要途径。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>广泛阅读不同类型的文章和书籍</li>
                            <li>分析你喜欢的作者的写作风格和技巧</li>
                            <li>注意作者如何开始和结束一个段落</li>
                            <li>收集好的句子和表达方式，理解为什么它们有效</li>
                        </ul>
                    </div>
                    
                    <!-- 卡片3 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">修改与编辑</h2>
                        <p class="mb-4">好的写作往往来自于反复修改。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>写完初稿后，放置一段时间再回来修改</li>
                            <li>大声朗读你的文章，找出不自然的部分</li>
                            <li>请他人阅读并提供反馈</li>
                            <li>关注一次只修改一个方面（内容、结构、语法等）</li>
                        </ul>
                    </div>
                    
                    <!-- 卡片4 -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover">
                        <h2 class="text-xl font-bold mb-4 text-primary-600 dark:text-primary-400">反馈与成长</h2>
                        <p class="mb-4">通过反馈不断改进你的写作。</p>
                        <ul class="list-disc pl-5 space-y-2">
                            <li>积极寻求他人的建设性反馈</li>
                            <li>参加写作小组或工作坊</li>
                            <li>分析你过去的写作，找出进步和不足</li>
                            <li>设定具体的写作目标和计划</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚区域 -->
    <footer class="bg-white dark:bg-gray-800 py-8 mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="border-t border-gray-200 dark:border-gray-700 pt-8">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">关于作者</h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-4">李明，专业写作教练与内容创作者，致力于帮助人们掌握自然、有效的写作技巧。</p>
                        <p class="text-gray-600 dark:text-gray-400">十年写作培训经验，曾出版《像说话一样写作》等多部写作指南。</p>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">联系方式</h3>
                        <div class="flex space-x-4 mb-4">
                            <a href="https://github.com/liming-writer" class="text-gray-500 hover:text-primary-500 dark:hover:text-primary-400 transition-colors duration-300">
                                <i class="fab fa-github text-2xl"></i>
                            </a>
                            <a href="https://twitter.com/liming_writer" class="text-gray-500 hover:text-primary-500 dark:hover:text-primary-400 transition-colors duration-300">
                                <i class="fab fa-twitter text-2xl"></i>
                            </a>
                            <a href="https://linkedin.com/in/liming-writer" class="text-gray-500 hover:text-primary-500 dark:hover:text-primary-400 transition-colors duration-300">
                                <i class="fab fa-linkedin text-2xl"></i>
                            </a>
                        </div>
                        <p class="text-gray-600 dark:text-gray-400">© 2023 李明. 保留所有权利。</p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // 检测系统主题偏好并设置初始主题
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }

        // 主题切换功能
        document.getElementById('theme-toggle').addEventListener('click', function() {
            document.documentElement.classList.toggle('dark');
        });

        // 标签页切换功能
        document.querySelectorAll('.tab-link').forEach(function(tab) {
            tab.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 移除所有标签页的活动状态
                document.querySelectorAll('.tab-link').forEach(function(t) {
                    t.classList.remove('active', 'border-primary-500', 'text-primary-600', 'dark:text-primary-500');
                    t.classList.add('border-transparent', 'hover:text-primary-600', 'hover:border-primary-300');
                });
                
                // 添加当前标签页的活动状态
                this.classList.add('active', 'border-primary-500', 'text-primary-600', 'dark:text-primary-500');
                this.classList.remove('border-transparent', 'hover:text-primary-600', 'hover:border-primary-300');
                
                // 隐藏所有内容区域
                document.querySelectorAll('.tab-content').forEach(function(content) {
                    content.classList.remove('active');
                });
                
                // 显示当前内容区域
                const tabId = this.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // 页面加载时的淡入动画
        document.addEventListener('DOMContentLoaded', function() {
            document.body.classList.add('animate-fade-in');
        });
    </script>
</body>
</html>