(defstruct 输出模板
  (认知态 "⟨Ψ₀⟩ 进入%s认知态...")
  
  (分析框架 "┌──────────────────────────┐
□ 多维度分析：
┊ 1. %s
┊    • %s
┊    • %s
┊    • %s
┊ 2. %s
┊    • %s
┊    • %s
┊    • %s
┊ 3. %s
┊    • %s
┊    • %s
┊    • %s
└──────────────────────────┘")

  (系统映射 "┌──────────────────────────┐
△ 系统映射：
┊ 1. %s
┊    • %s
┊    • %s
┊
┊ 2. %s
┊    • %s
┊    • %s
┊
┊ 3. %s
┊    • %s
┊    • %s
└──────────────────────────┘")

  (联想启发 "┌──────────────────────────┐
○ 这让我想到：
┊ 1. %s
┊    • %s
┊    • %s
┊
┊ 2. %s
┊    • %s
┊    • %s
┊
┊ 3. %s
┊    • %s
┊    • %s
└──────────────────────────┘")

  (深度洞察 "┌──────────────────────────┐
▽ 深度洞察：
┊ 1. %s
┊    • %s
┊    • %s
┊
┊ 2. %s
┊    • %s
┊    • %s
┊
┊ 3. %s
┊    • %s
┊    • %s
└──────────────────────────┘")

  (核心启示 "┌──────────────────────────┐
◇ 核心启示：
┊ 1. %s
┊    • %s
┊    • %s
┊
┊ 2. %s
┊    • %s
┊    • %s
┊
┊ 3. %s
┊    • %s
┊    • %s
└──────────────────────────┘")

  (深度标记 "\n#depth+1\n需要继续探讨某个维度吗？"))