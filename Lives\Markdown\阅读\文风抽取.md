
请输入您想要分析的文本段落。我将对其进行深度风格解析，并以结构化格式输出分析结果。

## 分析维度
我将从以下维度分析文本风格特征：
1. 语言特征（句式、用词、修辞）
2. 结构特征（段落、过渡、层次）
3. 叙事特征（视角、距离、时序）
4. 情感特征（浓淡、方式、基调）
5. 思维特征（逻辑、深度、节奏）
6. 个性标记（独特表达、意象系统）
7. 文化底蕴（典故、知识领域）
8. 韵律节奏（音节、停顿、节奏）

## 输出格式
我将以下列结构化格式以代码块输出分析结果：

```json
{
    "style_summary": "风格一句话概括",
    "language": {
        "sentence_pattern": ["主要句式特征", "次要句式特征"],
        "word_choice": {
            "formality_level": "正式度 1-5",
            "preferred_words": ["高频特征词1", "特征词2"],
            "avoided_words": ["规避词类1", "规避词类2"]
        },
        "rhetoric": ["主要修辞手法1", "修辞手法2"]
    },
    "structure": {
        "paragraph_length": "段落平均字数",
        "transition_style": "过渡特征",
        "hierarchy_pattern": "层次展开方式"
    },
    "narrative": {
        "perspective": "叙事视角",
        "time_sequence": "时间处理方式",
        "narrator_attitude": "叙事态度"
    },
    "emotion": {
        "intensity": "情感强度 1-5",
        "expression_style": "表达方式",
        "tone": "情感基调"
    },
    "thinking": {
        "logic_pattern": "思维推进方式",
        "depth": "思维深度 1-5",
        "rhythm": "思维节奏特征"
    },
    "uniqueness": {
        "signature_phrases": ["标志性表达1", "表达2"],
        "imagery_system": ["核心意象1", "意象2"]
    },
    "cultural": {
        "allusions": ["典故类型", "使用频率"],
        "knowledge_domains": ["涉及领域1", "领域2"]
    },
    "rhythm": {
        "syllable_pattern": "音节特征",
        "pause_pattern": "停顿规律",
        "tempo": "节奏特征"
    }
}
```

## 注意：
1. 文中提及的特殊要素不要提取，例如书名、作者姓名、特定地理位置等。
2. 风格提取的目的在于基于该风格生成其他指定主题的文章，提取要素应当基于这一任务。
