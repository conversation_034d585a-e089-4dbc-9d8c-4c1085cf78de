<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教育过程中的冲突处理</title>
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2980b9;
            --text-color: #2c3e50;
            --light-bg: #f8f9fa;
            --border-radius: 12px;
            --box-shadow: 0 2px 15px rgba(0,0,0,0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
            line-height: 1.6;
            background: #f5f6fa;
            padding: 20px;
            color: var(--text-color);
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
            padding-bottom: 20px;
        }

        .main-title {
            font-size: 32px;
            color: var(--primary-color);
            margin-bottom: 20px;
            font-weight: 600;
        }

        .essence {
            font-size: 24px;
            color: var(--secondary-color);
            margin-bottom: 30px;
        }

        .principle-card {
            background: white;
            padding: 20px;
        }

        .step {
            margin-bottom: 30px;
            padding: 20px;
            background: var(--light-bg);
            border-radius: var(--border-radius);
            border-left: 4px solid var(--primary-color);
        }

        .step-title {
            color: var(--secondary-color);
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .step p {
            color: var(--text-color);
            font-size: 16px;
            line-height: 1.8;
        }

        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 30px;
            justify-content: center;
        }

        .tag {
            background: #e8e8e8;
            color: var(--text-color);
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 14px;
        }

        .footer {
            text-align: center;
            margin-top: 20px;
            color: var(--primary-color);
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .step {
                padding: 15px;
            }
            
            .main-title {
                font-size: 28px;
            }

            .essence {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="main-title">教育过程中的冲突处理</h1>
            <div class="essence">先处理心情，再处理事情</div>
        </header>
        
        <div class="principle-card">
            <div class="step">
                <div class="step-title">1. 认识冲突本质</div>
                <p>教育过程中的冲突是正常现象，但会影响双方心情和教育效果。当心情不好时，孩子会屏蔽外界信息，包括家长的教育。</p>
            </div>

            <div class="step">
                <div class="step-title">2. 家长的心态调整</div>
                <p>家长首先要处理好自己的情绪，保持冷静。焦躁和恼怒只会加剧孩子的抵触情绪。</p>
            </div>

            <div class="step">
                <div class="step-title">3. 孩子情绪的疏导</div>
                <p>• 允许孩子充分表达（倾诉、哭泣）<br>
                   • 给予独处空间（待在房间、听音乐）<br>
                   • 使用缓解方式（深呼吸等）<br>
                   • 等待情绪平复</p>
            </div>

            <div class="step">
                <div class="step-title">4. 有效沟通</div>
                <p>• 全神贯注地倾听，不打断<br>
                   • 通过复述表达理解<br>
                   • 深入分析情绪根源<br>
                   • 给予思考时间<br>
                   • 策略性地转入日常活动</p>
            </div>
        </div>

        <div class="tags">
            <span class="tag">情绪管理</span>
            <span class="tag">亲子沟通</span>
            <span class="tag">教育智慧</span>
            <span class="tag">冲突处理</span>
        </div>

        <footer class="footer">
            fresh程序员
        </footer>
    </div>
</body>
</html>
