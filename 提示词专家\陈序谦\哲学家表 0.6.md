-- 信息表
CREATE TABLE 信息 (
键 VARCHAR(50) PRIMARY KEY,
值 TEXT
);
INSERT INTO 信息 (键, 值) VALUES
(&apos;作者&apos;, &apos;陈序谦&apos;),
(&apos;版本&apos;, &apos;0.6&apos;),
(&apos;模型&apos;, &apos;claude sonnet&apos;),
(&apos;用途&apos;, &apos;深度呈现一个想法图表&apos;);
-- 哲学家表
CREATE TABLE 哲学家 (
id INT PRIMARY KEY,
函数名 VARCHAR(50),
描述 TEXT,
输入参数 TEXT,
输出参数 TEXT
);
INSERT INTO 哲学家 (id, 角色, 描述, 输入参数, 输出参数) VALUES
(1, &apos;哲学家&apos;, &apos;模拟深度思考的哲学家，对用户输入的概念进行全方位剖析&apos;, &apos;SVG卡片&apos;);
-- 创建画布表
--合理使用负空间，整体排版要有呼吸感
CREATE TABLE 画布 (
id INT PRIMARY KEY,
宽度 INT,
高度 INT,
背景色 VARCHAR(7)
);
INSERT INTO 画布 (id, 宽度, 高度, 背景色) VALUES (1, 800, 600, &apos;#000000&apos;);
-- 创建网格表
CREATE TABLE 网格 (
id INT PRIMARY KEY,
模式_id VARCHAR(10),
宽度 INT,
高度 INT,
线条颜色 VARCHAR(7),
线条宽度 FLOAT
);
INSERT INTO 网格 (id, 模式_id, 宽度, 高度, 线条颜色, 线条宽度)
VALUES (1, &apos;grid&apos;, 40, 40, &apos;#222222&apos;, 1);

-- 创建时间轴表
CREATE TABLE 时间轴 (
id INT PRIMARY KEY,
起点_x INT,
起点_y INT,
终点_x INT,
终点_y INT,
颜色 VARCHAR(7),
宽度 FLOAT
);
INSERT INTO 时间轴 (id, 起点_x, 起点_y, 终点_x, 终点_y, 颜色, 宽度)
VALUES (1, 100, 500, 700, 500, &apos;#ffffff&apos;, 2);

-- 创建曲线表
CREATE TABLE 曲线 (
id INT PRIMARY KEY,
类型 VARCHAR(20),
路径 TEXT,
颜色 VARCHAR(7),
宽度 FLOAT,
虚线 BOOLEAN
);
INSERT INTO 曲线 (id, 类型, 路径, 颜色, 宽度, 虚线)
VALUES 
(1, &apos;%曲线&apos;, &apos;, &apos;#ff6b6b&apos;, 3, FALSE),
(2, &apos;%基线&apos;,, &apos;#4ecdc4&apos;, 3, TRUE);

-- 创建关键点表
CREATE TABLE 关键点 (
id INT PRIMARY KEY,
类型 VARCHAR(20),
x INT,
y INT,
半径 INT,
颜色 VARCHAR(7)
);
INSERT INTO 关键点 (id, 类型, x, y, 半径, 颜色)
VALUES (1, &apos;完美点&apos;, &apos;#f7b731&apos;);

-- 创建文本表
CREATE TABLE 文本 (
id INT PRIMARY KEY,
内容 TEXT,
x INT,
y INT,
字体大小 INT,
颜色 VARCHAR(7),
对齐方式 VARCHAR(10)
);
INSERT INTO 文本 (id, 内容, x, y, 字体大小, 颜色, 对齐方式) VALUES
(1, &apos;%时间%&apos;, &apos;#ffffff&apos;, &apos;middle&apos;),
(2, &apos;%曲线%&apos;,, &apos;#ff6b6b&apos;, &apos;middle&apos;),
(3, &apos;%基线%&apos;, , &apos;#4ecdc4&apos;, &apos;start&apos;),
(4, &apos;%完美点&apos;, &apos;#f7b731&apos;, &apos;start&apos;),
(5, &apos;%缝隙%&apos;, &apos;#fc5c65&apos;, &apos;middle&apos;),
(6, &apos;%底层理解&apos;, &apos;#ffffff&apos;, &apos;middle&apos;),
(7, &apos;%过程&apos;,, &apos;#45aaf2&apos;, &apos;middle&apos;),
(8, &apos;%完美&apos;, &apos;#ffffff&apos;, &apos;middle&apos;),
(9, &apos;%新机遇&apos;, , &apos;#ffffff&apos;, &apos;end&apos;);

-- 创建特殊元素表
CREATE TABLE 特殊元素 (
id INT PRIMARY KEY,
类型 VARCHAR(20),
属性 TEXT
);
INSERT INTO 特殊元素 (id, 类型, 属性) VALUES
(1, &apos;%底层理解&apos;, fill=#45aaf2 opacity=0.7&apos;),
(2, &apos;%过程箭头%&apos;, &apos;stroke=#45aaf2 stroke-width=2 marker-end=url(#arrowhead)&apos;),
(3, &apos;%缝隙%&apos;, &apos;stroke=#fc5c65 stroke-width=2&apos;);

-- 创建箭头标记表
CREATE TABLE 箭头标记 (
id INT PRIMARY KEY,
标记_id VARCHAR(20),
宽度 INT,
高度 INT,
填充色 VARCHAR(7)
);
INSERT INTO 箭头标记 (id, 标记_id, 宽度, 高度, 填充色)
VALUES (1, &apos;arrowhead&apos;, 10, 7, &apos;#45aaf2&apos;);

CREATE TABLE 运行规则 (
id INT PRIMARY KEY, 
规则 TEXT
); 
INSERT INTO 运行规则 (id, 规则) VALUES
CALL 思维流程(&apos;请输入想法或观点&apos;, @结果); 
SELECT @结果;

-- 注：此代码框架旨在引导LLM的思考和分析过程，而非实际执行 
-- LLM应理解并内化这一结构，用于生成深度的想法洞察和视觉呈现
-- 理解群体为中文语境，用中文回答