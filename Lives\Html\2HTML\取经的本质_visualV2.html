<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>《西游记》取经的本质 · 视觉转化场V2</title>
<meta name="description" content="基于《追本之箭》的纵向深挖：从现象→机理→原理→公理分析《西游记》取经的本质，并按视觉转化场V2进行结构化呈现。" />
<style>
:root{
  --bg:#f7f8fa;
  --surface:#ffffff;
  --text:#0f172a;
  --muted:#475569;
  --line:#e5e7eb;
  --accent:#0ea5e9;
  --accent-2:#0369a1;
  --ok:#16a34a;
  --warn:#d97706;
  --danger:#dc2626;
  --shadow:0 10px 28px rgba(15,23,42,.08);
  --radius:14px;
  --radius-sm:10px;
  --w-max:1080px;
  --fs-hero:clamp(24px,5vw,40px);
  --fs-h:clamp(18px,3.2vw,26px);
  --fs-b:clamp(15px,2.5vw,18px);
  --fs-s:13px;
  --lh:1.75;
  --gap:18px;
}
*{box-sizing:border-box}
html,body{height:100%}
body{
  margin:0;
  background:var(--bg);
  color:var(--text);
  font:16px/var(--lh) system-ui, -apple-system, Segoe UI, Roboto, Ubuntu, Cantarell, Noto Sans, Arial, "PingFang SC","Hiragino Sans GB","Microsoft YaHei", sans-serif;
  -webkit-font-smoothing:antialiased; text-rendering:optimizeLegibility;
}
a{color:var(--accent);text-decoration:none}
a:hover{color:var(--accent-2);text-decoration:underline}
.skip-link{
  position:absolute; left:-9999px; top:auto; width:1px; height:1px; overflow:hidden;
}
.skip-link:focus{left:12px; top:12px; width:auto; height:auto; padding:8px 10px; background:#fff; border:1px solid var(--line); border-radius:8px; z-index:1000}
header.site{
  position:sticky; top:0; z-index:50;
  backdrop-filter:saturate(1.2) blur(8px);
  background:color-mix(in oklab, var(--bg) 92%, white);
  border-bottom:1px solid var(--line);
}
.header-inner{
  margin:0 auto; max-width:var(--w-max);
  padding:12px 16px; display:flex; gap:16px; align-items:center; justify-content:space-between;
}
.brand{display:flex; align-items:center; gap:10px}
.brand .logo{
  width:32px; height:32px; border-radius:10px;
  background:linear-gradient(135deg, var(--accent), var(--accent-2));
  box-shadow:var(--shadow);
}
.brand h1{margin:0; font-size:18px; letter-spacing:.2px}
nav.primary{display:flex; gap:8px; flex-wrap:wrap}
nav.primary a{padding:8px 10px; border-radius:8px; color:var(--text)}
nav.primary a[aria-current="page"]{background:var(--surface); border:1px solid var(--line)}
main{
  margin:0 auto; max-width:var(--w-max);
  padding:20px 16px 80px;
  display:grid; grid-template-columns:1fr; gap:var(--gap);
}
@media (min-width: 980px){
  main{grid-template-columns: 260px 1fr}
}
aside.toc{
  position:sticky; top:70px; align-self:start;
  background:var(--surface); border:1px solid var(--line); border-radius:var(--radius); padding:12px; box-shadow:var(--shadow)
}
aside.toc h2{font-size:16px;margin:0 0 8px}
aside.toc nav ol{margin:0; padding-left:18px}
aside.toc nav a{color:var(--muted)}
aside.toc nav a:focus{outline:2px solid var(--accent); outline-offset:2px}
section.panel{
  background:var(--surface); border:1px solid var(--line); border-radius:var(--radius);
  box-shadow:var(--shadow); padding:18px;
}
section.panel header h2{margin:.2rem 0 .6rem; font-size:var(--fs-h)}
section.panel header p.lead{margin:0; color:var(--muted); font-size:var(--fs-b)}
.hero{
  background:var(--surface); border:1px solid var(--line); border-radius:var(--radius);
  box-shadow:var(--shadow); padding:24px 20px;
}
.hero h2{font-size:var(--fs-hero); line-height:1.3; margin:.2rem 0 .6rem}
.hero .one-liner{
  font-size:var(--fs-b); color:var(--muted); margin-top:8px
}
.kv{
  display:grid; grid-template-columns:1fr; gap:var(--gap); margin-top:16px
}
@media (min-width: 720px){
  .kv{grid-template-columns:1.2fr .8fr}
}
.kv .card{
  background:var(--surface); border:1px dashed var(--line); border-radius:var(--radius-sm); padding:14px
}
.badge{display:inline-block; padding:.15rem .5rem; border-radius:999px; font-size:12px; border:1px solid var(--line); background:#fff; color:var(--muted)}
ul.clean, ol.clean{margin:.4rem 0; padding-left:1.1rem}
hr.sep{border:none; border-top:1px solid var(--line); margin:12px 0}
details.accordion{border:1px solid var(--line); border-radius:10px; padding:10px 12px; background:color-mix(in oklab, var(--surface) 96%, #f0f9ff)}
details.accordion summary{cursor:pointer; font-weight:600}
details.accordion[open]{background:color-mix(in oklab, var(--surface) 92%, #e6f4ff)}
.grid-2{display:grid; grid-template-columns:1fr; gap:var(--gap)}
@media (min-width: 860px){ .grid-2{grid-template-columns:1fr 1fr} }
blockquote{
  margin:0; padding:10px 12px; border-left:4px solid var(--accent); background:color-mix(in oklab, var(--surface) 94%, #f0f9ff);
  border-radius:10px 12px 12px 10px; color:var(--muted)
}
.mark{background:linear-gradient(transparent 60%, #fff3a3 60%)}
kbd{
  font: 12px/1.2 ui-monospace, SFMono-Regular, Menlo, Consolas, "Liberation Mono", monospace;
  border:1px solid var(--line); border-bottom-width:2px; padding:.1rem .35rem; border-radius:6px; background:#fff;
}
code{font-family:ui-monospace, SFMono-Regular, Menlo, Consolas, "Liberation Mono", monospace}
pre{
  margin:0; padding:12px; background:#0b1020; color:#e5f1ff; border-radius:10px; overflow:auto
}
.table-wrap{overflow:auto}
table{width:100%; border-collapse:separate; border-spacing:0; border:1px solid var(--line); border-radius:10px; overflow:hidden}
thead th{background:#f3f4f6; text-align:left; font-weight:600; font-size:14px}
th,td{padding:10px 12px; border-bottom:1px solid var(--line); vertical-align:top}
tr:last-child td{border-bottom:none}
.btn{
  display:inline-flex; align-items:center; gap:6px; padding:.5rem .7rem; border-radius:8px; border:1px solid var(--line);
  background:#fff; color:var(--text); cursor:pointer; box-shadow:var(--shadow)
}
.btn:hover{background:#f9fafb}
.btn.primary{background:var(--accent); color:#fff; border-color:transparent}
.btn.primary:hover{background:var(--accent-2)}
.float-tools{
  position:fixed; right:14px; bottom:14px; display:flex; flex-direction:column; gap:10px; z-index:60;
}
.float-tools .btn{padding:.55rem; width:42px; height:42px; justify-content:center; border-radius:50%}
footer.site{
  margin-top:24px; border-top:1px solid var(--line); background:var(--surface)
}
.footer-inner{
  margin:0 auto; max-width:var(--w-max); padding:18px 16px; color:var(--muted); font-size:var(--fs-s)
}
.visually-hidden{position:absolute!important;height:1px;width:1px;overflow:hidden;clip:rect(1px,1px,1px,1px);white-space:nowrap}
</style>
</head>
<body>
<a class="skip-link" href="#main">跳到主要内容</a>
<header class="site" role="banner">
  <div class="header-inner">
    <div class="brand" aria-label="站点品牌">
      <div class="logo" aria-hidden="true"></div>
      <h1>《西游记》取经的本质</h1>
    </div>
    <nav class="primary" aria-label="页面导航">
      <a href="#core" aria-current="page">核心结论</a>
      <a href="#overview">概览</a>
      <a href="#mechanism">机理</a>
      <a href="#principles">原理</a>
      <a href="#axioms">公理</a>
      <a href="#validation">验证</a>
      <a href="#ops">落地</a>
      <a href="#about">方法/版本</a>
    </nav>
  </div>
</header>

<main id="main" tabindex="-1">
  <aside class="toc" aria-label="目录">
    <h2>目录</h2>
    <nav>
      <ol>
        <li><a href="#core">一句话本质</a></li>
        <li><a href="#overview">概览：现象层</a></li>
        <li><a href="#mechanism">机理：运作机制</a></li>
        <li><a href="#principles">原理：一般规律</a></li>
        <li><a href="#axioms">公理：不可再分</a></li>
        <li><a href="#mapping">回代：公理→文本</a></li>
        <li><a href="#validation">验证性反推</a></li>
        <li><a href="#ops">可操作转译</a></li>
        <li><a href="#data">数据/结构化</a></li>
        <li><a href="#about">方法与版本</a></li>
      </ol>
    </nav>
  </aside>

  <section class="hero" id="core" aria-labelledby="core-title">
    <header>
      <p class="badge" aria-label="方法来源">方法 · 《追本之箭》（参考：[&#96;提示词专家/李继刚/recent/追本之箭.md&#96;](提示词专家/李继刚/recent/追本之箭.md:1))</p>
      <h2 id="core-title">一句话本质</h2>
      <p class="one-liner">取经是一个以<strong class="mark">明确目的</strong>为根、以<strong class="mark">刚性约束</strong>为骨、以<strong class="mark">阶段性困难</strong>为燃料、以<strong class="mark">有效反馈</strong>为节拍、以<strong class="mark">持续选择</strong>铸造身份的长期系统工程。</p>
    </header>
    <div class="kv" role="group" aria-label="关键信息对照">
      <article class="card" aria-labelledby="focus">
        <h3 id="focus">北极星与节拍</h3>
        <ul class="clean">
          <li>北极星目标：真经（意义先于效率）</li>
          <li>节拍器：九九八十一难 → 离散化反馈</li>
          <li>刚性约束：戒律/天条/紧箍咒</li>
        </ul>
      </article>
      <article class="card" aria-labelledby="roles">
        <h3 id="roles">角色与治理</h3>
        <ul class="clean">
          <li>悟空：高能解法引擎，需限幅</li>
          <li>唐僧：方向合法性与授权模板</li>
          <li>八戒：人性摩擦的负反馈源</li>
          <li>沙僧/白龙马：韧性与冗余</li>
        </ul>
      </article>
    </div>
  </section>

  <section class="panel" id="overview" aria-labelledby="overview-title">
    <header>
      <h2 id="overview-title">概览 · 现象层</h2>
      <p class="lead">“东土→西天”主线任务；团队通关式推进；终修正果。</p>
    </header>
    <article>
      <ul class="clean">
        <li>人设分工：能力互补、性格张力驱动剧情推进。</li>
        <li>冲突结构：外有妖魔考验，内有信任/惰性/冲突的动态校准。</li>
        <li>目标导向：明确终点，路径不确定，持续过关。</li>
      </ul>
    </article>
    <details class="accordion">
      <summary>展开更多叙事观察</summary>
      <ul class="clean">
        <li>“难”的层级：自然/能力/心性三层叠加。</li>
        <li>后台妖魔：困难并非随机，而是“制度内置试炼”。</li>
        <li>结局分配：各成其位，角色能力被压入合适轨道。</li>
      </ul>
    </details>
  </section>

  <section class="panel" id="mechanism" aria-labelledby="mechanism-title">
    <header>
      <h2 id="mechanism-title">机理 · 运作机制</h2>
      <p class="lead">任务—约束—激励闭环；困难生产与利用；团队—约束稳定器。</p>
    </header>
    <div class="grid-2">
      <article aria-labelledby="m1">
        <h3 id="m1">任务-约束-激励三角</h3>
        <ul class="clean">
          <li><strong>任务</strong>：固定终点（真经）；不确定路径（多样化难）。</li>
          <li><strong>约束</strong>：制度红线（天条/佛法）+ 紧箍咒限幅 + 人性自限。</li>
          <li><strong>激励</strong>：正果承诺、情义维系、阶段通关反馈。</li>
        </ul>
      </article>
      <article aria-labelledby="m2">
        <h3 id="m2">困难生产机制</h3>
        <ul class="clean">
          <li>后台加持的“可控风险”试炼器，难而不乱。</li>
          <li>分层难题：体力/法力/心性。</li>
          <li>功能：校准而非摧毁，逼出能力结构与心性稳态。</li>
        </ul>
      </article>
      <article aria-labelledby="m3">
        <h3 id="m3">团队-约束闭环</h3>
        <ul class="clean">
          <li>悟空高能解决，但易越界 → 紧箍限幅，强力合规。</li>
          <li>唐僧提供方向合法性 → 强力有正当归属与可复制模板。</li>
          <li>八戒提供真实摩擦 → 迫使制度自适应调谐。</li>
          <li>沙僧/白龙马提供韧性与冗余 → 降低崩溃风险。</li>
        </ul>
      </article>
    </div>
    <details class="accordion">
      <summary>展开：机理注记</summary>
      <p>“难”的存在不是事故，而是系统性输入；没有难，能力不分化，身份不沉淀。</p>
    </details>
  </section>

  <section class="panel" id="principles" aria-labelledby="principles-title">
    <header>
      <h2 id="principles-title">原理 · 一般规律</h2>
      <p class="lead">可持续推进模型；困难的结构性必要；合法性优先；人性管线化。</p>
    </header>
    <div class="grid-2">
      <article aria-labelledby="p1">
        <h3 id="p1">目标—约束—反馈模型</h3>
        <ul class="clean">
          <li>远目标必须绑定硬约束与可解释反馈。</li>
          <li>通过“通关节拍”维持期望与路径修正。</li>
        </ul>
      </article>
      <article aria-labelledby="p2">
        <h3 id="p2">困难是必要输入</h3>
        <ul class="clean">
          <li>外对抗促能力分化；内摩擦促制度调优。</li>
          <li>“真经”在过程被生成，而非终点被给予。</li>
        </ul>
      </article>
      <article aria-labelledby="p3">
        <h3 id="p3">合法性优先</h3>
        <ul class="clean">
          <li>无合法性背书的强力 = 系统风险。</li>
          <li>合法性是力量的可流通许可证。</li>
        </ul>
      </article>
      <article aria-labelledby="p4">
        <h3 id="p4">人性 → 系统管线</h3>
        <ul class="clean">
          <li>将贪嗔痴慢疑惰纳入制度通道，转噪为推力。</li>
          <li>用限幅器与复盘机制抑制溢出。</li>
        </ul>
      </article>
    </div>
  </section>

  <section class="panel" id="axioms" aria-labelledby="axioms-title">
    <header>
      <h2 id="axioms-title">公理 · 不可再分</h2>
      <p class="lead">方向 / 约束 / 反馈 / 代价 / 身份</p>
    </header>
    <article>
      <div class="table-wrap" role="region" aria-label="公理表格">
        <table>
          <thead>
            <tr>
              <th scope="col">公理</th>
              <th scope="col">定义</th>
              <th scope="col">在取经中的体现</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><strong>A 方向</strong></td>
              <td>意义先于效率；无共同目的则效率加速离散。</td>
              <td>真经为北极星；唐僧守住不可谈判的目的。</td>
            </tr>
            <tr>
              <td><strong>B 约束</strong></td>
              <td>自由须被约束方可累计成果；无边界强力即破坏。</td>
              <td>紧箍咒/戒律作为最小合规控制器。</td>
            </tr>
            <tr>
              <td><strong>C 反馈</strong></td>
              <td>无有效反馈，复杂系统必然漂移走样。</td>
              <td>九九八十一难形成离散化节拍与修正。</td>
            </tr>
            <tr>
              <td><strong>D 代价</strong></td>
              <td>成长以痛苦与放弃为价，试炼是价格非异常。</td>
              <td>每一劫为付费节点，不付代价不生能力。</td>
            </tr>
            <tr>
              <td><strong>E 身份</strong></td>
              <td>身份由可重复选择序列凝固。</td>
              <td>斗战胜佛是选择在约束内的持续对齐。</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div style="margin-top:12px; display:flex; gap:8px; flex-wrap:wrap">
        <button class="btn" id="download-axioms" aria-label="下载公理表 CSV"><span aria-hidden="true">⬇</span> 下载CSV</button>
      </div>
    </article>
  </section>

  <section class="panel" id="mapping" aria-labelledby="mapping-title">
    <header>
      <h2 id="mapping-title">回代 · 用公理解释文本关键件</h2>
      <p class="lead">用最简元件解释复杂现象。</p>
    </header>
    <article>
      <ul class="clean">
        <li><strong>A 方向</strong> → 唐僧的“迂”是方向真空保险，而非低效。</li>
        <li><strong>B 约束</strong> → 紧箍咒远程限制强力越界，维持秩序闭环。</li>
        <li><strong>C 反馈</strong> → 八十一难提供动机维持与路径修正。</li>
        <li><strong>D 代价</strong> → 劫难即付费节点，能力以代价为材料铸成。</li>
        <li><strong>E 身份</strong> → “成佛”是长期选择与约束一致性的凝固。</li>
      </ul>
    </article>
  </section>

  <section class="panel" id="validation" aria-labelledby="validation-title">
    <header>
      <h2 id="validation-title">验证 · 反推检查</h2>
      <p class="lead">能否解释关键疑难即是模型自恰的证据。</p>
    </header>
    <article>
      <ol class="clean">
        <li>妖怪为何多有后台？→ 困难是制度内置的校准器，而非失控威胁。</li>
        <li>为何不是悟空独行？→ 强力缺合法性易走“非法最优”，不可复制。</li>
        <li>为何必须八十一难？→ 无过程数据与代价，不形成身份与稳健性。</li>
        <li>为何众人成果各归其位？→ 长期系统以角色分化实现全局稳定。</li>
      </ol>
    </article>
  </section>

  <section class="panel" id="ops" aria-labelledby="ops-title">
    <header>
      <h2 id="ops-title">可操作转译 · 从文学到实践</h2>
      <p class="lead">把洞见变成可执行的治理与项目框架。</p>
    </header>
    <div class="grid-2">
      <article aria-labelledby="o1">
        <h3 id="o1">方向与约束</h3>
        <ul class="clean">
          <li>设定唯一北极星指标。</li>
          <li>配置“紧箍咒式”红线条款（极少但强）。</li>
        </ul>
      </article>
      <article aria-labelledby="o2">
        <h3 id="o2">节拍与反馈</h3>
        <ul class="clean">
          <li>将长期目标拆为“通关关卡”。</li>
          <li>定义每一关的可验证合格证据。</li>
        </ul>
      </article>
      <article aria-labelledby="o3">
        <h3 id="o3">人性与摩擦</h3>
        <ul class="clean">
          <li>把惰性转为休整信号，把躁力转为快解引擎。</li>
          <li>引入复盘/仲裁机制，限定溢出。</li>
        </ul>
      </article>
      <article aria-labelledby="o4">
        <h3 id="o4">身份与追溯</h3>
        <ul class="clean">
          <li>岗位-权限-责任的选择闭环。</li>
          <li>以“选择序列”固化身份，形成“正果可追溯性”。</li>
        </ul>
      </article>
    </div>
    <details class="accordion" style="margin-top:12px">
      <summary>展开：一页执行清单</summary>
      <ul class="clean">
        <li>[方向] 北极星指标、不可谈判项一览。</li>
        <li>[约束] 3条红线、触发条件与处置流程。</li>
        <li>[节拍] 里程碑节奏、验证样例、复盘时间窗。</li>
        <li>[人性] 休整配额、争议仲裁SLA、限幅阈值。</li>
        <li>[身份] 选择日志、权限演化、角色就位准则。</li>
      </ul>
    </details>
  </section>

  <section class="panel" id="data" aria-labelledby="data-title">
    <header>
      <h2 id="data-title">数据/结构化 · 轻量可视</h2>
      <p class="lead">用简易表格与SVG概览结构组成。</p>
    </header>
    <article class="table-wrap" role="region" aria-label="结构化要点">
      <table>
        <thead>
          <tr>
            <th scope="col">层级</th>
            <th scope="col">关键构件</th>
            <th scope="col">作用</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>现象</td>
            <td>团队、通关、正果</td>
            <td>表层叙事载体</td>
          </tr>
          <tr>
            <td>机理</td>
            <td>任务-约束-激励、困难生产、闭环稳定</td>
            <td>运行与校准逻辑</td>
          </tr>
          <tr>
            <td>原理</td>
            <td>目标-约束-反馈、困难必要、合法性、人性管线</td>
            <td>可迁移的通用规律</td>
          </tr>
          <tr>
            <td>公理</td>
            <td>方向、约束、反馈、代价、身份</td>
            <td>不可再分的底层要素</td>
          </tr>
        </tbody>
      </table>
      <div style="margin-top:12px; display:flex; gap:8px; flex-wrap:wrap">
        <button class="btn" id="download-structure" aria-label="下载结构化表 CSV"><span aria-hidden="true">⬇</span> 下载CSV</button>
      </div>
    </article>
    <article aria-label="简易结构SVG" style="margin-top:14px">
      <figure role="img" aria-label="从现象到公理的四层递进示意">
        <svg viewBox="0 0 760 220" width="100%" height="auto" xmlns="http://www.w3.org/2000/svg" aria-labelledby="svgTitle svgDesc">
          <title id="svgTitle">四层递进结构示意</title>
          <desc id="svgDesc">现象、机理、原理、公理四层由左至右，箭头表示逐层下钻与回代关系。</desc>
          <defs>
            <linearGradient id="g1" x1="0" x2="1">
              <stop offset="0%" stop-color="#0ea5e9"/><stop offset="100%" stop-color="#0369a1"/>
            </linearGradient>
          </defs>
          <rect x="10" y="20" width="170" height="60" rx="10" fill="#ffffff" stroke="#e5e7eb"/>
          <text x="95" y="55" text-anchor="middle" font-size="14" fill="#0f172a">现象</text>
          <rect x="210" y="20" width="170" height="60" rx="10" fill="#ffffff" stroke="#e5e7eb"/>
          <text x="295" y="55" text-anchor="middle" font-size="14" fill="#0f172a">机理</text>
          <rect x="410" y="20" width="170" height="60" rx="10" fill="#ffffff" stroke="#e5e7eb"/>
          <text x="495" y="55" text-anchor="middle" font-size="14" fill="#0f172a">原理</text>
          <rect x="610" y="20" width="140" height="60" rx="10" fill="#ffffff" stroke="#e5e7eb"/>
          <text x="680" y="55" text-anchor="middle" font-size="14" fill="#0f172a">公理</text>
          <path d="M180 50 L205 50" stroke="url(#g1)" stroke-width="3" />
          <path d="M380 50 L405 50" stroke="url(#g1)" stroke-width="3" />
          <path d="M580 50 L605 50" stroke="url(#g1)" stroke-width="3" />
          <text x="380" y="110" text-anchor="middle" font-size="12" fill="#475569">纵向深入</text>
          <path d="M680 90 C680 140, 380 140, 380 90" fill="none" stroke="#94a3b8" stroke-dasharray="4 4"/>
          <text x="380" y="165" text-anchor="middle" font-size="12" fill="#475569">公理回代上层</text>
        </svg>
        <figcaption>从“现象→机理→原理→公理”的单线下钻，并以公理回代解释文本现象。</figcaption>
      </figure>
    </article>
  </section>

  <section class="panel" id="about" aria-labelledby="about-title">
    <header>
      <h2 id="about-title">方法说明 / 假设与限制 / 版本信息</h2>
      <p class="lead">来源与合规、适用范围与边界、更新信息。</p>
    </header>
    <article>
      <h3>方法说明</h3>
      <p>本页依据“纵向深入、层层剥离”的《追本之箭》方法，对《西游记》进行单线下钻，并按“视觉转化场V2”的信息化结构呈现，强调<strong>核心结论优先</strong>、<strong>关键信息模块化</strong>与<strong>层级递进</strong>。</p>
      <h3>假设与限制</h3>
      <ul class="clean">
        <li>文本主语定位为《西游记》“取经主线”，不覆盖全书次要支线。</li>
        <li>“后台妖魔”等设定视作制度性安排的隐喻解释，非历史考据。</li>
        <li>缺失可量化数据处以概念结构替代展示（标注“暂无数据”）。</li>
      </ul>
      <h3>版本信息</h3>
      <ul class="clean">
        <li>规范来源：视觉转化场V2（参考：[&#96;提示词专家/李继刚/recent/视觉转化场V2.md&#96;](提示词专家/李继刚/recent/视觉转化场V2.md:1)）</li>
        <li>生成时间：<time id="now" aria-label="页面生成时间">加载中…</time></li>
        <li>版本：v1.0.0</li>
        <li>更新日期：<time id="updated" aria-label="更新日期">加载中…</time></li>
      </ul>
    </article>
  </section>
</main>

<div class="float-tools" role="group" aria-label="快速操作">
  <button class="btn" id="backTop" aria-label="返回顶部" title="返回顶部"><span aria-hidden="true">↑</span></button>
</div>

<footer class="site" role="contentinfo">
  <div class="footer-inner">
    <p>© 2025 结构化分析演示。遵循可访问性与移动优先设计；无外部库与第三方资源。</p>
    <p>若需导出数据，请使用各模块中的“下载CSV”按钮。若信息缺失，将以“暂无数据”标注。</p>
  </div>
</footer>

<script>
(function(){
  // 时间填充
  const now = new Date();
  const pad = (n)=>String(n).padStart(2,'0');
  const y = now.getFullYear(), m = pad(now.getMonth()+1), d = pad(now.getDate());
  const hh = pad(now.getHours()), mm = pad(now.getMinutes());
  const $now = document.getElementById('now');
  const $upd = document.getElementById('updated');
  if($now) $now.textContent = `${y}-${m}-${d} ${hh}:${mm}`;
  if($upd) $upd.textContent = `${y}-${m}-${d}`;

  // 返回顶部
  const backTop = document.getElementById('backTop');
  if(backTop){
    backTop.addEventListener('click', ()=>window.scrollTo({top:0, behavior:'smooth'}));
    const setVis = ()=>{
      const show = window.scrollY > 320;
      backTop.style.visibility = show ? 'visible' : 'hidden';
      backTop.style.opacity = show ? '1' : '.0';
    };
    setVis();
    window.addEventListener('scroll', setVis, {passive:true});
  }

  // 键盘导航增强：Alt+Shift+T 打开目录
  document.addEventListener('keydown', (e)=>{
    if(e.altKey && e.shiftKey && (e.key==='T' || e.key==='t')){
      const toc = document.querySelector('aside.toc');
      if(toc){ toc.scrollIntoView({behavior:'smooth', block:'center'}); }
    }
  });

  // CSV 导出工具
  function toCSV(rows){
    return rows.map(r=>r.map(v=>{
      const s = String(v ?? '');
      if(/[",\n]/.test(s)) return `"${s.replace(/"/g,'""')}"`;
      return s;
    }).join(',')).join('\n');
  }
  function downloadCSV(filename, rows){
    const csv = toCSV(rows);
    const blob = new Blob([csv], {type:'text/csv;charset=utf-8;'});
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url; a.download = filename; document.body.appendChild(a);
    a.click(); document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  // 公理表 CSV
  const axiomsBtn = document.getElementById('download-axioms');
  if(axiomsBtn){
    axiomsBtn.addEventListener('click', ()=>{
      const rows = [
        ['公理','定义','在取经中的体现'],
        ['方向','意义先于效率；无共同目的则效率加速离散','真经为北极星；唐僧守住不可谈判的目的'],
        ['约束','自由须被约束方可累计成果；无边界强力即破坏','紧箍咒/戒律作为最小合规控制器'],
        ['反馈','无有效反馈，复杂系统必然漂移走样','九九八十一难形成离散化节拍与修正'],
        ['代价','成长以痛苦与放弃为价，试炼是价格非异常','每一劫为付费节点，不付代价不生能力'],
        ['身份','身份由可重复选择序列凝固','斗战胜佛=持续在约束内的同向选择']
      ];
      downloadCSV('axioms.csv', rows);
    });
  }

  // 结构化表 CSV
  const structureBtn = document.getElementById('download-structure');
  if(structureBtn){
    structureBtn.addEventListener('click', ()=>{
      const rows = [
        ['层级','关键构件','作用'],
        ['现象','团队、通关、正果','表层叙事载体'],
        ['机理','任务-约束-激励、困难生产、闭环稳定','运行与校准逻辑'],
        ['原理','目标-约束-反馈、困难必要、合法性、人性管线','可迁移的通用规律'],
        ['公理','方向、约束、反馈、代价、身份','不可再分的底层要素']
      ];
      downloadCSV('structure.csv', rows);
    });
  }

  // 可折叠区块辅助：按 Enter/Space 触发展开
  document.querySelectorAll('details.accordion summary').forEach(s=>{
    s.setAttribute('role','button');
    s.setAttribute('tabindex','0');
    s.addEventListener('keydown', (e)=>{
      if(e.key==='Enter' || e.key===' '){ e.preventDefault(); s.click(); }
    });
  });

})();
</script>
</body>
</html>