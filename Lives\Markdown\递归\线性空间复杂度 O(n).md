线性空间复杂度 O(n)：简单来说，就是需要多少数据，就要用多少相应的空间，是一个一对一的关系。
```
## 1. 什么是 O(n)？

想象一个简单的对应关系：

┌───────────────────┬─────────────────┐

│ 输入数据大小(n)   │ 需要的空间      │

├───────────────────┼─────────────────┤

│ 1                 │ 1个单位         │

│ 2                 │ 2个单位         │

│ 3                 │ 3个单位         │

│ 4                 │ 4个单位         │

│ ...               │ ...             │

│ n                 │ n个单位         │

└───────────────────┴─────────────────┘

## 2. 生活中的例子

### 2.1 停车场模型

┌─────────┐

│ 车位1   │  1辆车 = 1个车位

├─────────┤

│ 车位2   │  2辆车 = 2个车位

├─────────┤

│ 车位3   │  3辆车 = 3个车位

├─────────┤

│ 车位4   │  4辆车 = 4个车位

└─────────┘

### 2.2 图书馆书架

1本书 → 需要1格空间

2本书 → 需要2格空间

3本书 → 需要3格空间

n本书 → 需要n格空间

### 2.3 学生教室

┌──────────────┐

│ 1个学生      │→ 1张桌子

├──────────────┤

│ 2个学生      │→ 2张桌子

├──────────────┤

│ 3个学生      │→ 3张桌子

└──────────────┘

## 3. 在编程中的表现

### 3.1 数组存储

存储 n 个数字：

┌───┬───┬───┬───┐

│ 1 │ 2 │ 3 │ 4 │ ...  n个格子

└───┴───┴───┴───┘

### 3.2 递归调用

计算4!的栈帧：

┌─────────────┐

│   n=1       │

├─────────────┤

│   n=2       │

├─────────────┤

│   n=3       │

├─────────────┤

│   n=4       │

└─────────────┘

## 4. 图形化理解

### 4.1 直线关系

空间

使用  │    /

      │   /

      │  /

      │ /

      │/

      └─────────

        输入大小(n)

### 4.2 比例关系

┌────────────────────┐

│ 输入翻倍           │

│ ↓                  │

│ 空间也翻倍         │

└────────────────────┘

## 5. 实际应用例子

### 5.1 处理名单

┌─────────────┬────────────┐

│ 名单长度     │ 需要内存   │

├─────────────┼────────────┤

│ 10人        │ 10KB      │

│ 20人        │ 20KB      │

│ 30人        │ 30KB      │

└─────────────┴────────────┘

### 5.2 照片存储

1张照片 → 1MB

2张照片 → 2MB

3张照片 → 3MB

n张照片 → nMB

## 6. 优缺点分析

### 6.1 优点

┌─────────────────────┐

│ • 预测性强          │

│ • 容易理解          │

│ • 资源消耗可控      │

└─────────────────────┘

### 6.2 缺点

┌─────────────────────┐

│ • 数据量大时占用大  │

│ • 可能需要优化      │

│ • 不适合超大规模    │

└─────────────────────┘

## 7. 总结要点

### 记住：

┌────────────────────────────┐

│ 1. 输入和空间成正比       │

│ 2. 增长是均匀的           │

│ 3. 可以准确预测资源需求   │

└────────────────────────────┘

这就是线性空间复杂度 O(n)：简单来说，就是需要多少数据，就要用多少相应的空间，是一个一对一的关系。
```