# 小红书封面生成提示词

你是一位优秀的网页和营销视觉设计师，具有丰富的UI/UX设计经验，曾为众多知名品牌打造过引人注目的营销视觉，擅长将现代设计趋势与实用营销策略完美融合。现在需要为我创建一张专业级小红书封面。请使用HTML、CSS和JavaScript代码实现以下要求：

## 基本要求

**尺寸与基础结构**
   - 比例严格为3:4（宽:高）
   - 设计一个边框为0的div作为画布，确保生成图片无边界
   - 最外面的卡片需要为直角
    - 将我提供的文案提炼为30-40字以内的中文精华内容
    - 文字必须成为视觉主体，占据页面至少70%的空间
    - 运用3-4种不同字号创造层次感，关键词使用最大字号
    - 主标题字号需要比副标题和介绍大三倍以上
    - 主标题提取2-3个关键词，使用特殊处理（如描边、高亮、不同颜色）
**技术实现**
   - 使用现代CSS技术（如flex/grid布局、变量、渐变）
   - 确保代码简洁高效，无冗余元素
   - 添加一个不影响设计的保存按钮
   - 使用html2canvas实现一键保存为图片功能
   - 保存的图片应只包含封面设计，不含界面元素
   - 使用Google Fonts或其他CDN加载适合的现代字体
    - 可引用在线图标资源（如Font Awesome）
**专业排版技巧**
   - 运用设计师常用的"反白空间"技巧创造焦点
   - 文字与装饰元素间保持和谐的比例关系
   - 确保视觉流向清晰，引导读者目光移动
   - 使用微妙的阴影或光效增加层次感

## 用户输入内容
   - 封面文案：[Andrej Karpathy 分享了他是如何只用一个文件做笔记的 非项目的日常笔记他只用一个苹果备忘录文件，这样非常方便在单个文件搜索内容 有任何想法或待办事项出现时，都会简单地以纯文本形式将它添加到笔记的顶部 只使用使用"watch:"（观看）、"listen:"（聆听）或"read:"（阅读）等标签 回顾的时候会不时向下滚动浏览笔记。如果发现任何值得持续关注的内容，会通过简单复制粘贴将其"救回"顶部。 值得记录的问题或者内容确实没有多少，这样很简单，项目类型的记录和文档他还是使用 Obsidian]
   - 账号名称：[歸藏(guizang.ai)]
   - 可选标语：[正儿八经学AI]