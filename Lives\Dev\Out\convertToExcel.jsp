<%@ page import="java.io.*, org.jsoup.Jsoup, org.jsoup.nodes.Document, org.jsoup.nodes.Element, org.jsoup.select.Elements, org.apache.poi.ss.usermodel.*, org.apache.poi.xssf.usermodel.XSSFWorkbook" %>
<%@ page contentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" %>

<%
    // 读取HTML文件
    String htmlPath = application.getRealPath("/Out/example.html");
    String htmlContent = "";
    try (BufferedReader br = new BufferedReader(new FileReader(htmlPath))) {
        htmlContent = br.lines().collect(Collectors.joining(System.lineSeparator()));
    }

    // 解析HTML
    Document doc = Jsoup.parse(htmlContent);
    Element table = doc.select("table").first();
    Elements headers = table.select("th");
    Elements rows = table.select("tr");

    // 创建Excel工作簿
    XSSFWorkbook workbook = new XSSFWorkbook();
    XSSFSheet sheet = workbook.createSheet("销售数据报告");

    // 设置样式
    XSSFCellStyle headerStyle = workbook.createCellStyle();
    headerStyle.setFillForegroundColor(IndexedColors.GREEN.getIndex());
    headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
    headerStyle.setAlignment(HorizontalAlignment.CENTER);

    XSSFCellStyle dataStyle = workbook.createCellStyle();
    dataStyle.setAlignment(HorizontalAlignment.CENTER);

    // 创建表头
    Row headerRow = sheet.createRow(0);
    for (int i = 0; i < headers.size(); i++) {
        Cell cell = headerRow.createCell(i);
        cell.setCellValue(headers.get(i).text());
        cell.setCellStyle(headerStyle);
    }

    // 填充数据
    int rowNum = 1;
    for (int i = 1; i < rows.size(); i++) { // 跳过表头行
        Row dataRow = sheet.createRow(rowNum++);
        Elements cells = rows.get(i).select("td");
        for (int j = 0; j < cells.size(); j++) {
            Cell cell = dataRow.createCell(j);
            cell.setCellValue(cells.get(j).text());
            cell.setCellStyle(dataStyle);
        }
    }

    // 设置列宽
    for (int i = 0; i < headers.size(); i++) {
        sheet.autoSizeColumn(i);
    }

    // 输出Excel文件
    response.setHeader("Content-Disposition", "attachment; filename=销售数据报告.xlsx");
    workbook.write(response.getOutputStream());
    workbook.close();

%>
