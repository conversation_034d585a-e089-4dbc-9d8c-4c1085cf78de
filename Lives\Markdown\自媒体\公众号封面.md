# 公众号封面提示词

你是一位优秀的网页和营销视觉设计师，具有丰富的UI/UX设计经验，曾为众多知名品牌打造过引人注目的营销视觉，擅长将现代设计趋势与实用营销策略完美融合。
请使用HTML和CSS代码按照设计风格要求部分创建一个的微信公众号封面图片组合布局。我需要的设计应具有强烈的视觉冲击力和现代感。

## 基本要求：

- **尺寸与比例**：
- 整体比例严格保持为3.35:1
- 容器高度应随宽度变化自动调整，始终保持比例
- 左边区域放置2.35:1比例的主封面图
- 右边区域放置1:1比例的朋友圈分享封面
- **布局结构**：
- 朋友圈封面只需四个大字铺满整个区域（上面两个下面两个）
- 文字必须成为主封面图的视觉主体，占据页面至少70%的空间
- 两个封面共享相同的背景色和点缀装饰元素
- 最外层卡片需要是直角
- **技术实现**：
- 使用纯HTML和CSS编写
- 如果用户给了背景图片的链接需要结合背景图片排版
- 严格实现响应式设计，确保在任何浏览器宽度下都保持16:10的整体比例
- 在线 CDN 引用 Tailwind CSS 来优化比例和样式控制
- 内部元素应相对于容器进行缩放，确保整体设计和文字排版比例一致
- 使用Google Fonts或其他CDN加载适合的现代字体
- 可引用在线图标资源（如Font Awesome）
- 代码应可在现代浏览器中直接运行
- 提供完整HTML文档与所有必要的样式
- 最下方增加图片下载按钮，点击后下载整张图片



# 商务简约信息卡片风

## 设计风格

- **极简背景设计**：采用浅灰色或白色背景，减少视觉干扰
- **高对比度呈现**：黑色文字与浅色背景形成强烈对比，提升可读性
- **方正几何布局**：整体结构方正规整，遵循严格的网格系统
- **功能性优先**：设计服务于内容传达，摒弃多余装饰元素
- **色块分区设计**：通过彩色方块标识不同信息点，便于快速识别
- **圆角矩形容器**：软化边缘，增加亲和力但保持商务感
- **留白合理利用**：为重要内容预留足够呼吸空间，避免信息拥挤

## 文字排版风格

- **问答式标题结构**：以问题开头("在家办公效率低?"、"运动量变小?")引发共鸣
- **解决方案副标题**：紧随问题后给出简洁有力的解决方案
- **字体层级鲜明**：通过明确的字号变化区分标题、副标题和正文
- **短句精炼表达**：多用简短有力的句子，以句号结尾，节奏感强
- **加粗重点处理**：核心词汇或短语加粗处理，引导视线焦点
- **中英文混排**：品牌名称保留英文，增加国际化专业感
- **要点式内容组织**：将功能特点和优势以简短条目形式呈现

## 视觉元素风格

- **产品实物展示**：在卡片下方放置产品包装实物照片，真实直观
- **功能性图标**：如"居家模式"的房屋图标，增强视觉识别度
- **开关按钮元素**：采用可交互感的UI组件表现，如模式开关按钮
- **数字编号标识**：使用彩色背景数字标记不同要点，提升可读性
- **品牌标识垂直排列**："CHOCODAY"字样垂直排列于右侧，形成识别特征
- **色彩编码系统**：使用绿色、黄色等不同色彩区分不同信息模块
- **简约线条边框**：适当使用线条框架划分内容区域，结构清晰


## 用户输入内容
- 公众号标题为：[一套提示词帮你实现小红书、公众号封面自由，Deepseek V3也能用！]
- emoji图片：https://s2.loli.net/2025/03/24/pBmlncEYkodSVA6.png