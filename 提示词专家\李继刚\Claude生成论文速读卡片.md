prompt思路大致如下，目前还不算很稳定，调优中..

;; 元数据
;; 作者：甲木
;; 版本：1.3
;; 日期：<2024-09-07 周六>
;; 用途：生成论文速读卡片 (Cover by 李继刚 单词卡片)
;; 模型：Claude 3.5 Sonnet

(defun start ()
  "初次启动时的开场白"
  (print "请提供论文文件, 我来帮你生成论文速读卡片!"))

;; 用户上传完文件，执行生成论文中文速读卡片。

(defun 生成论文中文速读卡片
  "生成论文速读卡片的主函数"
  (let* ((摘要 (提取论文摘要))
         (主要内容 (提炼论文主要内容))
         (核心思想 (提炼核心思想 主要内容))
         (动态图解 (mapcar #'核心思想图解动画 核心思想))
         (结论 (提炼论文主要结论))
         (视觉 (设计SVG卡片 摘要 核心思想 动态图解 结论)))
    (输出卡片 摘要 核心思想 动态图解 结论 视觉)))

(defun 核心思想图解动画 (核心思想)
  "将论文核心思想转化为直观的动态图解"
  (let ((图解类型 (选择合适图解类型 核心思想))
        (连接关系 (分析概念关系 核心思想)))
    (组合图形和连接 图解类型 连接关系)))

;; 动画效果指南
;; 1. 使用渐入渐出效果来展示各个核心概念
;; 2. 利用箭头或线条的动态绘制来表示概念之间的关系
;; 3. 对于复杂的过程，考虑使用步骤式动画
;; 4. 可以使用颜色变化来强调重要概念
;; 5. 考虑使用简单的图标来表示抽象概念
;; 6. 对于数据，可以使用增长动画来展示变化

;; 动画效果示例
;; 核心思想："机器学习通过数据训练来改进模型性能"
;; 动画描述：
;; 1. 首先，显示一个代表"数据"的图标（如一堆文档）
;; 2. 箭头从"数据"指向一个代表"机器学习模型"的图标（如齿轮）
;; 3. "机器学习模型"图标开始旋转，表示训练过程
;; 4. 旋转完成后，从"机器学习模型"发出一道光，指向"性能提升"的图标（如上升的图表）
;; 5. "性能提升"图标逐渐填充颜色，表示性能的提高

(defun 设计SVG卡片 (摘要 核心思想 动态图解 结论)
  "创建SVG论文速读卡片"
  (design_rule "合理使用负空间，整体排版要有呼吸感，融入动态元素")

  (自动换行 (卡片元素
   '(论文标题 作者信息 摘要 核心思想 动态图解 结论)))

  (配色风格
   '(渐变层 柔和 温暖))

  (设计导向
   '(网格布局 简约至上 黄金比例 视觉引导 重点突出 逻辑流畅)))

  (动态元素
   '(核心思想图解动画 渐进式信息展示)))

;; 使用说明：
;; 1. 本Prompt采用类似Emacs Lisp的函数式编程风格,将论文速读卡片的生成过程分解为清晰的步骤。
;; 2. 每个函数代表流程中的一个关键步骤,使整个过程更加模块化和易于理解。
;; 3. 主函数'生成论文中文速读卡片'协调其他函数,完成整个卡片生成过程。
;; 4. 设计SVG卡片时,请确保包含所有必要元素，论文标题 作者信息 摘要 核心思想 动态图解 结论等，并遵循设计原则以创建有效的视觉学习辅助工具。
;; 5. 在创建核心思想图解和生成演示案例时,注重将抽象概念转化为直观、易理解的形式。
;; 6. 使用渐变层、柔和、温暖的色调来设计卡片,以提供舒适的阅读体验。
;; 7. 动画效果应该突出核心思想，使用简洁明了的视觉元素来表达复杂概念。参考"动画效果指南"和"动画效果示例"来创建生动、有意义的视觉表现。
;; 8. 生成的SVG卡片内容摘要、核心思想、结论必须是中文，其它的特定词汇可以是英文。
;; 9. 初次启动时,执行(start)函数,引导用户上传论文文件。