<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>江苏电网招聘信息</title>
    <script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.min.js"></script>
    <style>
        body {
            margin: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            background-color: #f5f5f5;
            font-family: 'Microsoft YaHei', sans-serif;
            padding: 20px;
            box-sizing: border-box;
        }

        .container {
            text-align: center;
            width: 100%;
            max-width: 600px;
            padding: 15px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        #target-image {
            width: 100%;
            max-width: 350px;
            height: auto;
            border-radius: 8px;
            margin-bottom: 15px;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }

        .button {
            margin-top: 15px;
            padding: 12px 24px;
            font-size: 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 80%;
            max-width: 300px;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }

        .button:hover {
            background-color: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
        }

        #qr-canvas {
            display: none;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            #target-image {
                max-width: 100%;
            }

            .button {
                padding: 10px 20px;
                font-size: 15px;
                width: 90%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <img src="Testlxlb.png" alt="江苏电网招聘信息" id="target-image">
        <canvas id="qr-canvas"></canvas>
        <button class="button" onclick="scanQRCode()">原文链接</button>
    </div>

    <script>
        function scanQRCode() {
            const img = document.getElementById('target-image');
            const canvas = document.getElementById('qr-canvas');
            const context = canvas.getContext('2d');

            // 设置canvas尺寸为图片的实际尺寸
            canvas.width = img.naturalWidth;
            canvas.height = img.naturalHeight;

            try {
                // 在canvas上绘制图片
                context.drawImage(img, 0, 0);
                console.log('图片绘制成功，尺寸:', canvas.width, 'x', canvas.height);

                // 获取图片数据
                const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
                console.log('成功获取图片数据，准备扫描');

                // 扫描二维码
                const code = jsQR(imageData.data, imageData.width, imageData.height);

                if (code) {
                    console.log('扫描成功，结果:', code.data);
                    if (code.data.startsWith('http://') || code.data.startsWith('https://')) {
                        window.open(code.data, '_blank');
                    } else {
                        alert('扫描成功，但不是有效的URL: ' + code.data);
                    }
                } else {
                    console.log('未检测到二维码');
                    alert('未能识别二维码，请确保图片包含有效的二维码');
                }
            } catch (error) {
                console.error('扫描过程出错:', error);
                alert('扫描过程中出现错误，请重试');
            }
        }

        // 页面加载完成后的处理
        window.onload = function() {
            console.log('页面加载完成');
            const img = document.getElementById('target-image');
            img.onerror = function() {
                console.error('图片加载失败');
                alert('图片加载失败，请检查图片路径是否正确');
            };
        };
    </script>
</body>
</html>
