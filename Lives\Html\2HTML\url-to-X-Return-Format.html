<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>URL转换工具</title>
    <style>
        body {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        #urlInput {
            padding: 10px;
            font-size: 16px;
            width: 100%;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
        }
        
        .button {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 16px;
            border-radius: 4px;
        }
        
        .button:hover {
            background-color: #45a049;
        }
        
        #copyBtn {
            background-color: #2196F3;
        }
        
        #copyBtn:hover {
            background-color: #1976D2;
        }
        
        #result {
            white-space: pre-wrap;
            border: 1px solid #ddd;
            padding: 15px;
            min-height: 200px;
            background-color: #f9f9f9;
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            overflow-x: auto;
        }
        
        #result img {
            max-width: 100%;
            height: auto;
            margin: 10px 0;
            border: 1px solid #ddd;
        }
        
        .loading {
            display: none;
            text-align: center;
            color: #666;
        }
        
        .format-select {
            padding: 10px;
            font-size: 16px;
            width: 200px;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .result-container {
            position: relative;
        }

        .image-controls {
            display: none;
            margin-top: 10px;
            gap: 10px;
        }

        .image-controls.visible {
            display: flex;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>URL转换工具</h1>
        <input type="text" id="urlInput" placeholder="请输入要转换的URL">
        <div class="controls">
            <select id="formatSelect" class="format-select">
                <option value="markdown">Markdown</option>
                <option value="html">HTML</option>
                <option value="text">纯文本</option>
                <option value="screenshot">截图</option>
                <option value="pageshot">网页快照</option>
            </select>
            <button id="convertBtn" class="button">转换为Markdown</button>
        </div>
        <div class="loading" id="loading">转换中...</div>
        <div class="button-group">
            <button id="copyBtn" class="button">复制内容</button>
        </div>
        <div class="result-container">
            <div id="result"></div>
            <div class="image-controls" id="imageControls">
                <button class="button" id="downloadBtn">下载图片</button>
            </div>
        </div>
    </div>

    <script>
        const convertBtn = document.getElementById('convertBtn');
        const urlInput = document.getElementById('urlInput');
        const result = document.getElementById('result');
        const loading = document.getElementById('loading');
        const formatSelect = document.getElementById('formatSelect');
        const copyBtn = document.getElementById('copyBtn');
        const imageControls = document.getElementById('imageControls');
        const downloadBtn = document.getElementById('downloadBtn');

        // 更新按钮文本
        formatSelect.addEventListener('change', () => {
            const format = formatSelect.value;
            const formatNames = {
                'markdown': 'Markdown',
                'html': 'HTML',
                'text': '纯文本',
                'screenshot': '截图',
                'pageshot': '网页快照'
            };
            convertBtn.textContent = `转换为${formatNames[format]}`;
            
            // 根据格式类型显示/隐藏复制按钮
            copyBtn.style.display = ['screenshot', 'pageshot'].includes(format) ? 'none' : 'block';
        });

        // 复制功能
        copyBtn.addEventListener('click', async () => {
            try {
                await navigator.clipboard.writeText(result.textContent);
                const originalText = copyBtn.textContent;
                copyBtn.textContent = '复制成功！';
                setTimeout(() => {
                    copyBtn.textContent = originalText;
                }, 2000);
            } catch (err) {
                alert('复制失败，请手动复制');
            }
        });

        // 下载图片
        downloadBtn.addEventListener('click', () => {
            const img = result.querySelector('img');
            if (img) {
                const a = document.createElement('a');
                a.href = img.src;
                a.download = 'screenshot.png';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            }
        });

        convertBtn.addEventListener('click', async () => {
            const url = urlInput.value.trim();
            
            if (!url) {
                alert('请输入URL');
                return;
            }

            try {
                loading.style.display = 'block';
                result.textContent = '';
                imageControls.classList.remove('visible');
                
                const proxyUrl = `https://r.jina.ai/${url}`;
                const response = await fetch(proxyUrl, {
                    headers: {
                        'X-Return-Format': formatSelect.value
                    }
                });

                if (!response.ok) {
                    throw new Error('网络请求失败');
                }

                const format = formatSelect.value;
                
                if (['screenshot', 'pageshot'].includes(format)) {
                    const blob = await response.blob();
                    const imgUrl = URL.createObjectURL(blob);
                    result.innerHTML = `<img src="${imgUrl}" alt="网页截图">`;
                    imageControls.classList.add('visible');
                } else {
                    const content = await response.text();
                    result.textContent = content;
                    
                    // 格式化HTML内容
                    if (format === 'html') {
                        result.innerHTML = content
                            .replace(/></g, '>\n<')
                            .replace(/</g, '&lt;')
                            .replace(/>/g, '&gt;');
                    }
                }
            } catch (error) {
                result.textContent = `错误: ${error.message}`;
            } finally {
                loading.style.display = 'none';
            }
        });
    </script>
</body>
</html> 