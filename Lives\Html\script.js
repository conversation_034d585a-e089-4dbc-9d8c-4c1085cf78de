document.addEventListener('DOMContentLoaded', () => {
    const faces = document.querySelectorAll('.face');
    const resetButton = document.querySelector('.reset-button');
    const cube = document.querySelector('.cube');

    // Add click event to each face to unfold it
    faces.forEach(face => {
        face.addEventListener('click', () => {
            face.classList.add('unfolded');
        });
    });

    // Reset button folds the cube back up
    resetButton.addEventListener('click', () => {
        // Remove 'unfolded' class from all faces
        faces.forEach(face => {
            face.classList.remove('unfolded');
        });

        // Optional: Reset the cube's overall rotation to its initial view
        // We do this by removing the transform, letting it transition, then adding it back
        cube.style.transform = 'translateZ(-100px) rotateX(0deg) rotateY(0deg)'; // A neutral state
        setTimeout(() => {
            cube.style.transform = 'translateZ(-100px) rotateX(-30deg) rotateY(-45deg)'; // The initial tilted state
        }, 50); // A small delay to ensure the transition is triggered
    });
});
