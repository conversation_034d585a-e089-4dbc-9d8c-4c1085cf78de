<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>西游记 - 文字视觉场</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@300;400;700&family=Noto+Sans+SC:wght@300;400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            min-height: 100vh;
            font-family: 'Noto Serif SC', serif;
            overflow-x: hidden;
        }
        
        .visual-field {
            width: 440px;
            margin: 0 auto;
            position: relative;
            min-height: 100vh;
            background: rgba(255, 255, 255, 0.02);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        /* 场的记忆 */
        .field-memory {
            position: absolute;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            font-family: 'Noto Sans SC', sans-serif;
        }
        
        .logo {
            top: 20px;
            right: 20px;
            font-weight: 300;
            letter-spacing: 2px;
        }
        
        .timestamp {
            top: 20px;
            left: 20px;
            font-weight: 300;
        }
        
        /* 思想的战场 */
        .battlefield {
            padding: 80px 40px 60px;
            position: relative;
        }
        
        .title {
            font-size: 36px;
            font-weight: 700;
            color: #ff6b35;
            text-align: center;
            margin-bottom: 60px;
            position: relative;
            font-family: 'Noto Serif SC', serif;
        }
        
        .title::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 2px;
            background: linear-gradient(90deg, transparent, #ff6b35, transparent);
        }
        
        /* 三重转化的视觉呈现 */
        .transformation {
            margin-bottom: 50px;
            position: relative;
        }
        
        .density-crystal {
            background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(255, 107, 53, 0.05));
            border-left: 3px solid #ff6b35;
            padding: 25px;
            margin-bottom: 30px;
            border-radius: 0 8px 8px 0;
        }
        
        .crystal-point {
            font-size: 16px;
            line-height: 1.8;
            color: #ffffff;
            margin-bottom: 15px;
            font-weight: 400;
        }
        
        .crystal-point:last-child {
            margin-bottom: 0;
        }
        
        .crystal-point strong {
            color: #ff6b35;
            font-weight: 700;
        }
        
        /* 色彩律的渐变显现 */
        .color-flow {
            height: 80px;
            background: linear-gradient(90deg, 
                #ff6b35 0%,     /* 炽热金红 */
                #4a90e2 50%,    /* 深沉靛蓝 */
                #f4d03f 100%    /* 温润金黄 */
            );
            margin: 40px 0;
            border-radius: 40px;
            position: relative;
            overflow: hidden;
        }
        
        .color-flow::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, 
                transparent 25%, 
                rgba(255, 255, 255, 0.1) 25%, 
                rgba(255, 255, 255, 0.1) 50%, 
                transparent 50%, 
                transparent 75%, 
                rgba(255, 255, 255, 0.1) 75%
            );
            background-size: 20px 20px;
            animation: flow 3s linear infinite;
        }
        
        @keyframes flow {
            0% { transform: translateX(-20px); }
            100% { transform: translateX(20px); }
        }
        
        /* 张力律的对峙 */
        .tension-field {
            display: grid;
            grid-template-columns: 1fr 60px 1fr;
            gap: 0;
            margin: 40px 0;
            align-items: center;
        }
        
        .sans-force {
            font-family: 'Noto Sans SC', sans-serif;
            font-weight: 300;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            text-align: right;
            line-height: 1.6;
        }
        
        .tension-center {
            width: 2px;
            height: 60px;
            background: linear-gradient(to bottom, transparent, #ff6b35, transparent);
            margin: 0 auto;
        }
        
        .serif-elegance {
            font-family: 'Noto Serif SC', serif;
            font-weight: 400;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }
        
        /* 底部余韵 */
        .afterglow {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            color: rgba(255, 255, 255, 0.4);
            font-family: 'Noto Sans SC', sans-serif;
            text-align: center;
            line-height: 1.5;
        }
        
        /* 呼吸效果 */
        .breathing {
            animation: breathe 4s ease-in-out infinite;
        }
        
        @keyframes breathe {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }
        
        /* 微妙的粒子效果 */
        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(255, 107, 53, 0.3);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .particle:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
        .particle:nth-child(2) { top: 60%; right: 15%; animation-delay: 2s; }
        .particle:nth-child(3) { bottom: 30%; left: 20%; animation-delay: 4s; }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) scale(1); opacity: 0.3; }
            50% { transform: translateY(-20px) scale(1.2); opacity: 0.8; }
        }
    </style>
</head>
<body>
    <div class="visual-field">
        <!-- 场的记忆 -->
        <div class="field-memory logo breathing">文字视觉场</div>
        <div class="field-memory timestamp">2025.01.31</div>
        
        <!-- 粒子效果 -->
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        
        <!-- 思想的战场 -->
        <div class="battlefield">
            <h1 class="title">西游记</h1>
            
            <!-- 密度律凝结 -->
            <div class="transformation">
                <div class="density-crystal">
                    <div class="crystal-point">
                        <strong>反叛之心</strong><br>
                        从石猴到齐天大圣的桀骜不驯
                    </div>
                    <div class="crystal-point">
                        <strong>束缚之路</strong><br>
                        紧箍咒下的痛苦修行与自我救赎
                    </div>
                    <div class="crystal-point">
                        <strong>觉悟之光</strong><br>
                        西天取经中的慈悲与智慧觉醒
                    </div>
                </div>
            </div>
            
            <!-- 色彩律流动 -->
            <div class="color-flow"></div>
            
            <!-- 张力律对峙 -->
            <div class="tension-field">
                <div class="sans-force">
                    野性<br>
                    自由<br>
                    反叛
                </div>
                <div class="tension-center"></div>
                <div class="serif-elegance">
                    修行<br>
                    智慧<br>
                    圆满
                </div>
            </div>
        </div>
        
        <!-- 底部余韵 -->
        <div class="afterglow">
            心猿意马终归一处<br>
            西天路上见真如
        </div>
    </div>
</body>
</html>