* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #f39c12;
    --text-color: #333;
    --bg-color: #f7f7f7;
    --card-bg: #fff;
    --hover-bg: #fbfbfb;
    --shadow: 0 2px 10px rgba(0,0,0,0.05);
    --hover-shadow: 0 2px 8px rgba(0,0,0,0.05);
    --border-color: rgba(0,0,0,0.05);
    --border-color-light: rgba(243, 156, 18, 0.2);
}

body {
    font-family: "Microsoft YaHei", sans-serif;
    line-height: 1.6;
    padding: 20px;
    background: var(--bg-color);
    color: var(--text-color);
    font-size: 14px;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background: var(--card-bg);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    padding: 30px;
}

.page-title {
    text-align: center;
    font-size: 26px;
    margin-bottom: 25px;
    position: relative;
    padding-bottom: 12px;
    color: var(--primary-color);
}

.page-title::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
}

.section {
    background: var(--card-bg);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid var(--border-color);
    animation: fadeIn 0.5s ease forwards;
}

.section-title {
    font-size: 18px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    color: var(--primary-color);
}

.section-title::before {
    content: "";
    width: 3px;
    height: 18px;
    margin-right: 10px;
    background: var(--primary-color);
    border-radius: 2px;
}

.list-item {
    position: relative;
    padding: 12px 20px;
    margin: 8px 0;
    background: var(--hover-bg);
    border-radius: 6px;
    border-left: 2px solid var(--primary-color);
    transition: all 0.2s ease;
    font-size: 14px;
}

.list-item:hover {
    background: var(--card-bg);
    transform: translateX(5px);
    box-shadow: var(--hover-shadow);
}

.tags {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
    margin-top: 25px;
    padding: 15px 0;
}

.tags span {
    padding: 4px 12px;
    background: var(--primary-color);
    color: white;
    border-radius: 15px;
    font-size: 12px;
    transition: all 0.2s ease;
}

.tags span:hover {
    transform: translateY(-2px);
    box-shadow: var(--hover-shadow);
}

.footer {
    text-align: center;
    color: var(--primary-color);
    font-size: 14px;
    font-style: italic;
    margin-top: 25px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color-light);
}

strong {
    color: var(--primary-color);
    font-weight: 500;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 768px) {
    body {
        padding: 15px;
    }

    .container {
        padding: 20px;
    }

    .page-title {
        font-size: 22px;
        margin-bottom: 20px;
    }

    .section-title {
        font-size: 16px;
    }

    .list-item {
        padding: 10px 15px;
        font-size: 13px;
    }
} 