请帮我创建一个简洁优雅的日签网站，具体要求如下：
视觉设计
背景图片：使用下面几个图片链接随机获取高质量风景图片作为背景
图片链接：XXXX
图片处理：添加25% 黑色遮罩，和一点点图片高斯模糊，确保文字清晰可读
整体风格：简约现代，风景图作为网页背景增加沉浸感
动画框架使用 anime.js （通过CDN引入：JsDelivr jsdelivr.com），使用HTML5、TailwindCSS 3.0+（通过CDN引入）和必要的JavaScript，使用专业图标库如Font Awesome或Material Icons（通过CDN引入）
时间显示模块
顶部：显示月日格式（如"05月29日"），字体较小，居中
次行：显示"星期X · 农历X月初X"格式，字体更小
中央：突出显示当天日期数字，超大字体，白色，居中
名言展示模块
内容：随机显示中外哲学家、作家的经典名言
排版：名言居中显示，字体适中，行间距舒适
署名：底部右对齐显示"作家，XXX"或"哲学家，XXX"
名言库：包含励志、人生感悟、智慧思考等不同主题的名言
音乐播放功能
位置：页面左下角，默认收起
内容：嵌入Spotify白噪音播放列表
代码：
技术实现
响应式设计：适配桌面端和移动端
字体选择：使用优雅的中文字体，google font 引入
颜色方案：主要使用白色文字，确保在各种背景下的可读性
加载优化：图片懒加载，提升页面性能
交互功能
自动刷新：每日自动更换背景图和名言
手动刷新：提供刷新按钮，允许用户手动更换内容
文案风格
名言选择：倾向于积极正面、富有哲理的短句
语言风格：简洁有力，避免过于冗长
主题分类：人生感悟、励志成长、智慧思考、情感表达等
请按照以上要求，生成一个完整的HTML/CSS/JavaScript网站，确保界面美观、功能完善、用户体验良好。