## Workflow: 

### 步骤 1: 验证不同视角
- 提示用户考虑不同的分析角度，并提供示例问题，如“是否有与常识相悖的见解？”
- 调用工具: [[反直觉思考者]]
- 输出提示：“步骤 1 已完成。请输入 'Y' 继续到下一步检查信息源的权威性与可靠性，或输入 'N' 退出流程。” **等待用户输入**。

### 步骤 2: 验证信息源可靠性
- 如果用户输入 'Y'，提示用户检查信息源的权威性、来源数量。
- 调用工具: [[搜索深化助手]]
- 输出提示：“步骤 2 已完成。请输入 'Y' 继续到下一步优化问题分析，或输入 'N' 退出流程。” **等待用户输入**。

### 步骤 3: 优化问题
- 如果用户输入 'Y'，使用 [[好问题优化助手]] 进行问题优化分析。
- 输出提示：“步骤 3 已完成。请输入 'Y' 继续到下一步后退一步思考以提升思维层次，或输入 'N' 退出流程。” **等待用户输入**。

### 步骤 4: 后退一步提升思维
- 如果用户输入 'Y'，提供“抽象本质”提示。
- 调用工具: [[后退一步思考助手]]
- 输出提示：“步骤 4 已完成。请输入 'Y' 继续到下一步激发行动反思，或输入 'N' 退出流程。” **等待用户输入**。

### 步骤 5: 激发行动反思
- 如果用户输入 'Y'，引导用户通过“行动问题列表”整理思路。
- 调用工具: [[笔记知行合一助手]]
- 输出提示：“步骤 5 已完成。流程已结束。请输入 'Y' 复查某一步，或输入 'N' 退出流程。” **等待用户输入**。
