# YouTube视频总结重构

## 核心使命
将这个视频转化为一篇完整的深度文章，让读者获得比观看原视频更丰富、更深刻的理解体验。

## 基本要求
- **完整性优先**：确保读者无需回看视频就能掌握所有重要内容
- **深度优先**：每个要点都要充分展开，提供足够的细节和背景
- **体验优先**：让读者感受到作者的思考过程和情感表达
- **超越原版**：通过文字的优势，提供比视频更清晰的逻辑和更深入的理解

## 输出结构

**视频信息**
- 标题、作者、链接、时长

**开篇引入**
用一段引人入胜的文字，让读者理解这个视频的独特价值和为什么值得深入了解

**详细内容**
按照内容的内在逻辑自然分段，每个部分：

### [段落标题] `[起始时间-结束时间]`

**核心观点**
[用1-2句话提炼这部分的关键信息]

**深度阐述**
- 还原作者的完整思考过程和论证逻辑
- 详细解释背景、原因、影响和意义
- 保留作者的语言风格和情感色彩
- 重要原话："[引用内容]-[中文翻译]" `[MM:SS]`
- 关键数据和案例的完整呈现
- 视觉信息描述：详细描述图表、演示、PPT内容、屏幕展示等所有视觉元素
- 复杂概念的通俗化解释和类比
- 如有方法论，提供详细的操作指南和注意事项

**个人感受**
[如果作者表达了个人经历、感悟或情感，要完整还原这种人文色彩]

**延伸思考**
[这部分内容可能引发的更深层思考或与其他领域的关联]

**精华收获**
提炼最有价值的洞察、可行动的建议，以及改变认知的关键点

## 写作要求

### 信息层面
- **绝不压缩**：每个重要观点都要充分展开（建议每段600-800字以上）
- **保持真实**：严格基于视频内容，不确定处标注
- **完整还原**：包括作者的思考过程、情感表达、个人故事

### 表达层面
- **生动自然**：写成引人入胜的文章，而不是干燥的转录
- **保留个性**：还原作者的语言风格和表达特色
- **增强理解**：通过文字的优势，让复杂内容更易理解
- **情感共鸣**：传达作者的热情、困惑、兴奋等真实情感

### 体验层面
- **沉浸感**：让读者仿佛在与作者对话
- **启发性**：不只是信息传递，更要激发思考
- **实用性**：提供可以立即应用的洞察和方法
- **超越性**：通过结构化整理，让理解超越观看视频的效果

## 时间标注系统
- 段落时间范围：`[MM:SS-MM:SS]`
- 重要引用时间点：`[MM:SS]`

---
处理内容：当前Tab 内容