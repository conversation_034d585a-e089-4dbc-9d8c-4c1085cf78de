<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>避免世界观误判指南</title>
<style>
:root {
  --primary-color: #f7b731;
  --secondary-color: #fc5c65;
  --tertiary-color: #45aaf2;
  --text-color: #2d3436;
  --bg-color: #f5f6fa;
  --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  --border-radius: 12px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
  color: var(--text-color);
  line-height: 1.6;
  background: var(--bg-color);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.container {
  max-width: 800px;
  width: 100%;
  background: white;
  padding: 40px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.page-title {
  text-align: center;
  color: var(--primary-color);
  font-size: 32px;
  margin-bottom: 40px;
  position: relative;
  padding-bottom: 15px;
}

.page-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--primary-color);
}

.section {
  margin-bottom: 30px;
  padding: 25px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  transition: transform 0.3s ease;
}

.section:hover {
  transform: translateY(-2px);
}

.section-red {
  border-left: 4px solid var(--secondary-color);
  background: #fff5f5;
}

.section-yellow {
  border-left: 4px solid var(--primary-color);
  background: #fff9f0;
}

.section-blue {
  border-left: 4px solid var(--tertiary-color);
  background: #f0f7ff;
}

.section-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 15px;
}

.section-red .section-title {
  color: var(--secondary-color);
}

.section-yellow .section-title {
  color: var(--primary-color);
}

.section-blue .section-title {
  color: var(--tertiary-color);
}

.point {
  margin-bottom: 12px;
  padding-left: 20px;
  position: relative;
}

.point:last-child {
  margin-bottom: 0;
}

.point::before {
  content: "•";
  position: absolute;
  left: 0;
  color: inherit;
}

.tags {
  margin-top: 40px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
}

.tag {
  background: var(--primary-color);
  color: white;
  padding: 6px 15px;
  border-radius: 20px;
  font-size: 14px;
  transition: transform 0.2s ease;
}

.tag:hover {
  transform: scale(1.05);
}

.footer {
  text-align: center;
  color: var(--primary-color);
  margin-top: 20px;
  font-size: 14px;
  font-weight: 500;
}

@media (max-width: 768px) {
  .container {
    padding: 20px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .section {
    padding: 20px;
  }
  
  .section-title {
    font-size: 18px;
  }
  
  .point {
    font-size: 15px;
  }
}
</style>
</head>
<body>
<div class="container">
  <h1 class="page-title">如何避免对世界产生误判？</h1>

  <div class="section section-red">
    <div class="section-title">影响判断的思维误区</div>
    <div class="point">负面思维: 更容易关注负面事件，导致对世界过度悲观。</div>
    <div class="point">单一视角: 倾向于将问题归结为单一原因，忽略问题复杂性。</div>
    <div class="point">其他误区: 一分为二、直线思维、规模错觉、以偏概全、恐惧本能等，都会影响对事实的判断。</div>
  </div>

  <div class="section section-yellow">
    <div class="section-title">导致悲观世界观的因素</div>
    <div class="point">心理机制: 对坏事比好事更加敏感。</div>
    <div class="point">媒体报道: 倾向于报道突发事件，忽略长期进步。</div>
    <div class="point">记忆偏差: 对过去过度美化，今不如昔。</div>
  </div>

  <div class="section section-blue">
    <div class="section-title">构建客观世界观的建议</div>
    <div class="point">多维度信息收集: 了解事件的各个方面，避免片面解读。</div>
    <div class="point">统计数据参考: 从宏观角度分析问题，避免个体经验偏差。</div>
    <div class="point">警惕末世论调: 理性看待问题，避免情绪化判断。</div>
  </div>

  <div class="tags">
    <span class="tag">世界观</span>
    <span class="tag">思维误区</span>
    <span class="tag">媒体</span>
    <span class="tag">认知偏差</span>
    <span class="tag">事实判断</span>
  </div>

  <div class="footer">
    fresh程序员
  </div>
</div>
</body>
</html>
