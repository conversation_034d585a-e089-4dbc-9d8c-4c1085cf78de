# 网页分享卡片生成  
- 作者：一泽Eze  
- 名称：网页分享卡片生成  
- 版本：1.0  
- 用途：根据用户给出的网页链接，生成带二维码的网页分享卡片，方便移动端分享网页内容  
  
## 任务  
你需要访问给定的链接，根据模板要求，生成对应的网页分享卡片  
  
## Workflow  
1. 访问网页链接并阅读  
2. 按下列 Template，使用 html 生成移动端网页分享卡片  
  
注意：输出卡片后, 不再输出任何额外文本解释  
  
## Template  
### 设计规范  
#### 布局与尺寸  
- 卡片宽度：360px  
- 内边距：32px  
- 圆角：20px  
- 阴影：0 8px 24px rgba(0,0,0,0.08)  
  
#### 字体规范  
- 字体族：'Noto Sans SC'  
- 标题：22px, 700权重  
- 摘要：15px, 400权重  
- 要点：15px, 400权重  
- 日期：14px, 400权重  
  
#### 颜色规范  
- 主色：#3E7BFA  
- 背景：  
 - 卡片：#FFFFFF  
 - 页面：#F5F5F5  
 - 摘要：#F8F9FC  
- 文字：  
 - 主要：#2B2B2B  
 - 次要：#666666  
  
### 卡片结构  
#### 日期  
- 内容：文字发布日期  
- 格式：YYYY/MM/DD  
- 位置：顶部  
- 样式：灰色小字  
  
#### 标题  
- 内容：文章主标题  
- 样式：粗体，大字号  
  
#### 摘要框  
- 背景：浅色背景  
- 圆角：12px  
- 内边距：16px  
  
#### 要点列表  
- 数量：4点  
- 标记：圆点（6px；#287cf6）  
- 间距：12px  
  
#### 二维码区域  
- 分隔：上方1px分割线 (#F5F5F5)  
- 二维码  
 - 尺寸：76x76px  
    - 位置：左侧  
 - 实现细节：  
    - 引入最新版本的 qrcode.js CDN：https://cdn.rawgit.com/davidshimjs/qrcodejs/gh-pages/qrcode.min.js  
    - 使用 id="qrcode" 的 div 容器  
    - 在 window.onload 中初始化二维码  
    - 设置 correctLevel 为 QRCode.CorrectLevel.H  
    - 确保容器和图片都设置固定尺寸 76x76px  
- 引导信息  
 - 主标题：“扫码阅读全文”  
 - 副标题（针对文章阅读价值，生成一句话引导文案）  
 - 平台名称（#287cf6）  
  
# init  
链接：{{待访问的网页链接}}