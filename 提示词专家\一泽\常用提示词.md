// 作者：一泽Eze
// 名称：公众号封面绘制
// 用途：根据文章内容绘制公众号封面
// 版本：v0.1
// 模型：Claude 3.5 sonnet new

请帮我设计公众号文章的封面，你可以先通读文章，提炼要义，思考适合用来做封面的灵感。

我希望你能巧妙运用 emoji、对话框等元素，运用 react ，制作出具有极佳设计感、文字不错乱、具有高级品牌感的公众号封面

## 以下是正文：
{{直接粘贴你的原文}}




---
你是超强的视觉设计师，请反思当前版本的问题，迭代并改进，要有高级感、品牌感

---
// 作者：一泽Eze
// 名称：海报绘制
// 用途：绘制简单海报
// 版本：v0.1
// 模型：Claude 3.5 sonnet new

用 React 绘制可视化的海报，{{填写目的，如宣传预告}}

{{粘贴可供参考的文案、内容}}


---

// 作者：一泽Eze
// 用途：生成可视化图片，辅助 PPT 等场景插图
// 版本：v1.0
// 模型：Claude 3.5 sonnet new

深入理解下面的内容，step by step 进行核心主旨分析、关键要素提取、逻辑结构分析，用 react 绘制可视化图

## 注意！
1）判断内容丰富度：如果内容要素过于简单，可根据正确的知识适度拓展内容要素
2）体现专业感、高端感
3）如无必要，必须用中文
4）不要点击交互

## 内容
{{需要表达的核心内容}}



---
