# HTML数据看板生成器

## 核心任务：
请分析我提供的数据源 (\[文本/Excel/PDF] - 请指明文件类型并简述内容)，提取关键洞察和量化数据。构建一个单一、自包含的HTML文件，以专业、交互友好且视觉吸引人的数据看板形式展示这些信息。

## 关键要求：
1.  数据处理与转换：
    * 清晰阐述将原始输入数据转换为适用于可视化的结构化 JavaScript 对象/数组的逻辑。
    * 选择3-5个核心指标，使用适合数据类型的图表（折线图、柱状图、饼图等）
    * 仔细阅读内容，找出可量化的数据、关键趋势、公司案例或观点。

2.  可视化与交互性 (ECharts 核心)：
    * 主要库： 使用 ECharts 5.x 进行所有图表绘制。
    * 图表选型： 采用最能代表数据特性和洞察的 ECharts 图表类型（如折线图、柱状图、饼图/环形图、散点图、热力图等）。
    * 图表精细化： 图表必须精细调整，包含清晰的标题、坐标轴、标签、内容丰富的提示框 (Tooltip) 和可交互的图例 (Legend)。为数据密集的图表实现 `dataZoom`（区域缩放）。包含 `toolbox`（工具栏）以支持常用操作（保存图片、数据视图）。
    * 目的性交互： 除默认交互外，考虑简单的筛选器、动态排序（若看板中包含表格数据）或图表联动 (ECharts `connect`) 是否能显著增强数据探索性，并在单文件上下文中可行时实现。

3.  代码结构与技术 (单一HTML文件)：
    * HTML： 语义化的 HTML5 结构。为不同的看板区域（如KPI、主图表、详细分解）和图表元素使用明确的 `div` 容器。
    * CSS：
        * 在 `<style>` 标签内嵌入 CSS，或少量使用内联样式。
        * 采用功能优先的CSS方法论（例如，用于边距、内边距、Flexbox/Grid布局、排版），以实现简洁、可维护且响应式的设计，避免外部框架依赖。
    * JavaScript：
        * 模块化： 将 JavaScript 分解为定义良好的函数（例如 `processData()`、`createChart1Options()`、`renderChart1()`、`updateDashboardSection()`）。以此模拟组件化方式，提升代码清晰度。
        * 数据流： 清晰定义数据从处理到图表配置项及渲染的流程。
        * 动态内容： 如果能简化结构，使用 JavaScript 动态生成重复性元素（如信息卡片或列表项）的 HTML。
        * 事件处理： 确保 ECharts 实例在 `window.resize` 时能正确 `resize()` (考虑防抖优化性能)。

4.  设计与用户体验：
    * 主题与风格： \[建议一个：现代简约风、科技暗黑模式、商务专业风，或根据数据内容选择]。
    * 布局： 简洁、响应式，确保在不同设备（桌面、平板、移动端）上的可读性。关键信息应突出显示。
    * 色板： 使用专业且易于访问的调色板。如果涉及品牌颜色，\[请提供HEX色值]。

## 期望输出：
一个单一、完整的HTML文件，并且：
* 能在现代浏览器中无错直接运行。
* 完全响应式且视觉效果优良。
* 有效运用 ECharts 进行富有洞察力的数据可视化和交互。
* 包含结构良好且有注释的 HTML、CSS 和 JavaScript 代码。