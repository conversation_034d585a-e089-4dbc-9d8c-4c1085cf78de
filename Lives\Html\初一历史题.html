<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>初一历史知识测试</title>
    <style>
        body {
            background: #1a1a2e;
            color: #fff;
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .quiz-card {
            background: #16213e;
            border: 2px solid #4a90e2;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }
        .quiz-card.active {
            opacity: 1;
            transform: translateY(0);
        }
        .options {
            display: grid;
            gap: 10px;
            margin: 15px 0;
        }
        .option-btn {
            background: #233049;
            border: 1px solid #4a90e2;
            color: white;
            padding: 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 1em;
            text-align: left;
        }
        .option-btn:hover:not(:disabled) {
            background: #2a3b5a;
        }
        .option-btn.correct {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }
        .option-btn.wrong {
            background: #dc3545;
            color: white;
            border-color: #dc3545;
        }
        .progress-bar {
            width: 100%;
            height: 10px;
            background: #233049;
            border-radius: 5px;
            margin: 20px 0;
            overflow: hidden;
        }
        .progress {
            width: 0%;
            height: 100%;
            background: #4a90e2;
            border-radius: 5px;
            transition: width 0.3s ease;
        }
        .score-panel {
            font-size: 1.2em;
            text-align: center;
            background: #233049;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .explanation {
            background: #233049;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
            display: none;
            border-left: 4px solid #4a90e2;
        }
        .nav-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        button {
            background: #4a90e2;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s;
        }
        button:hover:not(:disabled) {
            background: #357abd;
        }
        button:disabled {
            background: #233049;
            cursor: not-allowed;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #4a90e2;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .header p {
            color: #888;
            font-size: 1.1em;
        }
        .wrong-question-item {
            background: #233049;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #dc3545;
        }
        .wrong-mark {
            color: #dc3545;
            margin-left: 5px;
        }
        .correct-mark {
            color: #28a745;
            margin-left: 5px;
        }
        .explanation-text {
            margin: 10px 0;
            padding: 10px;
            background: rgba(74, 144, 226, 0.1);
            border-radius: 5px;
        }
        .error-analysis {
            margin: 10px 0;
            padding: 10px;
            background: rgba(220, 53, 69, 0.1);
            border-radius: 5px;
        }
        .knowledge-tag {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            margin: 5px;
            background: #4a90e2;
            color: white;
        }
        .perfect-score {
            text-align: center;
            color: #28a745;
            font-size: 1.2em;
            padding: 20px;
        }
        .results-header {
            text-align: center;
            margin-bottom: 20px;
        }
        .final-score {
            font-size: 2em;
            color: #4a90e2;
            margin: 10px 0;
        }
        .score-chart {
            height: 20px;
            background: #1a1a2e;
            border-radius: 10px;
            margin: 15px 0;
            overflow: hidden;
        }
        #score-bar {
            height: 100%;
            background: linear-gradient(90deg, #4a90e2, #357abd);
            width: 0%;
            transition: width 1s ease-in-out;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <h1>初一历史知识测试</h1>
        <p>测试你的历史知识掌握程度</p>
    </div>

    <div class="progress-bar">
        <div class="progress" id="progress"></div>
    </div>

    <div class="score-panel">
        已完成: <span id="progress-text">1</span>/10题
    </div>

    <div id="quiz-container"></div>

    <div id="results-panel" style="display: none;" class="quiz-card active">
        <div class="results-header">
            <h2>测试完成！</h2>
            <div class="final-score">得分：<span id="final-score">0</span>/10</div>
            <div class="score-chart">
                <div id="score-bar"></div>
            </div>
            <p>正确率：<span id="accuracy">0</span>%</p>
        </div>

        <div class="wrong-questions">
            <h3>错题分析</h3>
            <div id="wrong-questions-list"></div>
        </div>

        <button onclick="restartQuiz()" class="restart-btn">重新测试</button>
    </div>

    <div class="nav-buttons">
        <button onclick="prevQuestion()" id="prev-btn">上一题</button>
        <button onclick="nextQuestion()" id="next-btn">下一题</button>
    </div>
</div>

<script>
const questions = [
    {
        question: "夏朝的建立者是谁？",
        options: ["禹", "启", "汤", "文王"],
        correct: 0,
        type: "choice",
        explanation: "禹是夏朝的建立者，他治理洪水有功，开创了中国第一个世袭制王朝。",
        knowledge: ["夏朝", "朝代更替"]
    },
    {
        question: "商朝灭亡的原因是（）导致的。",
        options: ["商纣王暴虐", "周武王伐纣", "商朝内乱", "自然灾害"],
        correct: 1,
        type: "choice",
        explanation: "周武王伐纣导致商朝灭亡，牧野之战是关键战役。",
        knowledge: ["商周", "战争史"]
    },
    {
        question: "春秋时期霸主齐桓公的著名谋士是谁？",
        options: ["管仲", "孙武", "商鞅", "吴起"],
        correct: 0,
        type: "choice",
        explanation: "管仲(管子)是齐桓公的宰相，辅佐齐桓公成为春秋五霸之首。",
        knowledge: ["春秋", "人物关系"]
    },
    {
        question: "判断题：秦始皇统一度量衡、统一货币、统一文字。",
        options: ["正确", "错误"],
        correct: 0,
        type: "judge",
        explanation: "这是正确的。秦始皇实行车同轨、书同文等重要的统一措施。",
        knowledge: ["秦朝", "制度政策"]
    },
    {
        question: "西汉时期最著名的外交使者是谁？",
        options: ["张骞", "班超", "郑和", "玄奘"],
        correct: 0,
        type: "choice",
        explanation: "张骞是汉武帝时期著名的外交家，两次出使西域，开辟丝绸之路。",
        knowledge: ["汉朝", "人物关系"]
    },
    {
        question: "判断题：唐朝是中国历史上疆域最大的朝代。",
        options: ["正确", "错误"],
        correct: 1,
        type: "judge",
        explanation: "这是错误的。元朝才是中国历史上疆域最大的朝代。",
        knowledge: ["朝代特征", "时间节点"]
    },
    {
        question: "四大发明中，最早出现的是：",
        options: ["指南针", "火药", "造纸术", "印刷术"],
        correct: 2,
        type: "choice",
        explanation: "造纸术最早，由蔡伦改进，大约在东汉时期。",
        knowledge: ["文化发展", "科技史"]
    },
    {
        question: "丝绸之路的开辟时期是在：",
        options: ["秦朝", "西汉", "东汉", "唐朝"],
        correct: 1,
        type: "choice",
        explanation: "丝绸之路是在西汉时期由张骞出使西域后逐渐形成的。",
        knowledge: ["时间节点", "文化发展"]
    },
    {
        question: "判断题：商朝是中国历史上第一个有文字记载的朝代。",
        options: ["正确", "错误"],
        correct: 0,
        type: "judge",
        explanation: "正确。商朝有甲骨文记载，是中国最早有文字记载的朝代。",
        knowledge: ["商朝", "文化发展"]
    },
    {
        question: "周朝实行的分封制的主要目的是：",
        options: ["削弱王权", "加强统治", "发展经济", "巩固边防"],
        correct: 1,
        type: "choice",
        explanation: "分封制的主要目的是加强统治，通过分封亲族功臣来管理广大疆土。",
        knowledge: ["制度政策", "周朝"]
    }
];

let currentQuestion = 0;
let score = 0;
let answered = new Array(questions.length).fill(false);
let userAnswers = new Array(questions.length).fill(null);

function showQuestion(index) {
    const container = document.getElementById('quiz-container');
    const question = questions[index];
    
    let html = `
        <div class="quiz-card active">
            <h3>第${index + 1}题：${question.question}</h3>
            <div class="options">
    `;
    
    question.options.forEach((option, i) => {
        let buttonClass = '';
        if (answered[index]) {
            if (i === question.correct) {
                buttonClass = 'correct';
            } else if (i === userAnswers[index]) {
                buttonClass = 'wrong';
            }
        }
        
        html += `
            <button class="option-btn ${buttonClass}" 
                    onclick="checkAnswer(${i})"
                    ${answered[index] ? 'disabled' : ''}>
                ${option}
            </button>
        `;
    });

    html += `
            </div>
            <div class="explanation" id="explanation-${index}" 
                 ${answered[index] ? 'style="display:block"' : ''}>
                ${question.explanation}
            </div>
        </div>
    `;

    container.innerHTML = html;
    updateProgress();
}

function getErrorAnalysis(question, userAnswer) {
    const errorAnalyses = {
        "朝代更替": "建议绘制历史朝代更替图，加强对朝代更替过程的理解。",
        "人物关系": "可以通过制作历史人物关系网络图，加深对历史人物之间联系的记忆。",
        "制度政策": "建议将各朝代重要制度政策进行对比学习，理解其特点和影响。",
        "文化发展": "可以通过时间轴梳理文化发展脉络，加强对文化演变的理解。",
        "时间节点": "建议制作重要历史事件时间表，强化对时间节点的记忆。",
        "科技史": "可以通过案例学习，深入理解古代科技发展的过程和影响。"
    };

    let mainKnowledge = question.knowledge[0];
    return errorAnalyses[mainKnowledge] || "建议仔细阅读教材相关章节，加强这部分知识的理解和记忆。";
}

function checkAnswer(selectedIndex) {
    if (answered[currentQuestion]) return;
    
    const question = questions[currentQuestion];
    userAnswers[currentQuestion] = selectedIndex;
    answered[currentQuestion] = true;
    
    if (selectedIndex === question.correct) {
        score++;
    }

    document.getElementById('progress-text').textContent = answered.filter(x => x).length;
    showQuestion(currentQuestion);

    if (isTestComplete()) {
        setTimeout(showResults, 1500);
    }
}

function isTestComplete() {
    return answered.every(a => a);
}

function showResults() {
    const quizContainer = document.getElementById('quiz-container');
    const resultsPanel = document.getElementById('results-panel');
    const wrongQuestionsList = document.getElementById('wrong-questions-list');
    
    quizContainer.style.display = 'none';
    resultsPanel.style.display = 'block';
    
    document.getElementById('final-score').textContent = score;
    const accuracy = (score / questions.length) * 100;
    document.getElementById('accuracy').textContent = accuracy.toFixed(1);
    document.getElementById('score-bar').style.width = `${accuracy}%`;
    
    let wrongQuestionsHtml = '';
    questions.forEach((q, index) => {
        if (userAnswers[index] !== q.correct) {
            wrongQuestionsHtml += `
                <div class="wrong-question-item">
                    <p><strong>题目 ${index + 1}:</strong> ${q.question}</p>
                    <p>你的答案: ${q.options[userAnswers[index]]} 
                        <span class="wrong-mark">✘</span></p>
                    <p>正确答案: ${q.options[q.correct]} 
                        <span class="correct-mark">✔</span></p>
                    <p class="explanation-text">${q.explanation}</p>
                    <div class="error-analysis">
                        <p><strong>错误分析与建议：</strong></p>
                        <p>${getErrorAnalysis(q, userAnswers[index])}</p>
                    </div>
                    <div class="knowledge-tags">
                        ${q.knowledge.map(k => 
                            `<span class="knowledge-tag">${k}</span>`).join('')}
                    </div>
                </div>
            `;
        }
    });
    wrongQuestionsList.innerHTML = wrongQuestionsHtml || 
        '<p class="perfect-score">恭喜！你已完全掌握所有知识点！</p>';
}

function nextQuestion() {
    if (currentQuestion < questions.length - 1) {
        currentQuestion++;
        showQuestion(currentQuestion);
    }
}

function prevQuestion() {
    if (currentQuestion > 0) {
        currentQuestion--;
        showQuestion(currentQuestion);
    }
}

function updateProgress() {
    const progress = ((currentQuestion + 1) / questions.length) * 100;
    document.getElementById('progress').style.width = `${progress}%`;
}

function restartQuiz() {
    currentQuestion = 0;
    score = 0;
    answered = new Array(questions.length).fill(false);
    userAnswers = new Array(questions.length).fill(null);
    document.getElementById('progress-text').textContent = '1';
    document.getElementById('quiz-container').style.display = 'block';
    document.getElementById('results-panel').style.display = 'none';
    showQuestion(0);
}

// 初始化显示第一题
showQuestion(0);
</script>
</body>
</html>
