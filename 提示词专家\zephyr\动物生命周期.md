动物生命周期
作者：空格 zephyr

(defun 生命周期生成器 ()
"生成xx 的生命周期SVG图表和描述"
(lambda (主题)
(let* ((生命阶段 (获取生命阶段 主题))
(科普数据 (获取科普数据 主题))
(背景样式 (设计背景 主题))
(时间轴 (创建时间轴 主题))
(阶段emoji (选择阶段emoji 主题))
(装饰emoji (选择装饰emoji 主题))
(副标题 (生成副标题 主题 科普数据)))
(创建优化SVG图表 主题 生命阶段 科普数据 背景样式 时间轴 阶段emoji 装饰emoji 副标题))))

(defun 获取生命阶段 (主题)
"获取主题的主要生命阶段"
(case 主题
(牛马 &apos;("学习期" "求学期" "就业期" "奋斗期" "稳定期"))
(t &apos;("初期" "成长期" "成熟期" "衰老期"))))

(defun 获取科普数据 (主题)
"获取主题的科普数据列表"
(case 主题
(牛马 &apos;(("0-18岁，接受基础教育，平均每天学习8小时，累计约52,560小时。"
"18-22岁，高等教育阶段，约70%的人选择继续深造，为未来工作做准备。"
"22-30岁，初入职场，平均工作时间9.7小时/天，约46%人经历过至少3份工作。"
"30-50岁，事业发展期，55%的人在同一公司工作超过5年，平均加班时间达1000小时/年。"
"50-65岁，职业生涯稳定期，约30%的人达到管理层位置，为退休生活做准备。")
"平均每个人一生中要工作约90,000小时，相当于连续工作10年不间断。"))
(t &apos;(("阶段1的数据描述"
"阶段2的数据描述"
"阶段3的数据描述"
"阶段4的数据描述"
"阶段5的数据描述")
"通用主题的有趣数据描述"))))

(defun 设计背景 (主题)
"根据主题设计适合的背景"
(case 主题
(牛马 &apos;(渐变 "FFF3E0" "FFE0B2" 地铁线))
(t &apos;(渐变 "F5F5F5" "E0E0E0" 通用))))

(defun 创建时间轴 (主题)
"创建主题生命周期的时间轴"
(case 主题
(牛马 &apos;("0岁" "18岁" "22岁" "30岁" "50岁" "65岁"))
(t &apos;("初期" "成长期" "成熟期" "后期" "衰老期"))))

(defun 选择阶段emoji (主题)
"选择与生命阶段相关的emoji"
(case 主题
(牛马 &apos;("📚" "" "" "" ""))
(t &apos;("🌱" "🌿" "🌳" ""))))

(defun 选择装饰emoji (主题)
"选择与主题相关的装饰emoji"
(case 主题
(牛马 &apos;(("🏙" 30 50 60) ("" 100 90 40) ("" 700 400 60) ("" 760 430 40) ("🐂" 180 40 40) ("" 620 460 40)))
(t &apos;(("🌱" 30 50 60) ("🌳" 700 400 60) ("" 100 90 40) ("🌞" 760 430 40)))))

(defun 生成副标题 (主题 科普数据)
"根据科普数据生成副标题"
(format "你知道吗？%s" (第二个元素 科普数据)))

(defun 创建优化SVG图表 (主题 生命阶段 科普数据 背景样式 时间轴 阶段emoji 装饰emoji 副标题)
"创建优化的生命周期SVG图表"
(let ((svg-template
"<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 800 500\">
<!-- 渐变背景 -->
<defs>
<linearGradient id=\"bgGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">
<stop offset=\"0%\" style=\"stop-color:#{背景颜色1};stop-opacity:1\" />
<stop offset=\"100%\" style=\"stop-color:#{背景颜色2};stop-opacity:1\" />
</linearGradient>
</defs>
<rect width=\"100%\" height=\"100%\" fill=\"url(#bgGradient)\" />

<!-- 地铁线条装饰 -->
<path d=\"M0,490 L800,490\" stroke=\"#FFB74D\" stroke-width=\"4\" />
<path d=\"M0,496 L800,496\" stroke=\"#FFA726\" stroke-width=\"4\" />
<circle cx=\"100\" cy=\"493\" r=\"3\" fill=\"#E65100\" />
<circle cx=\"300\" cy=\"493\" r=\"3\" fill=\"#E65100\" />
<circle cx=\"500\" cy=\"493\" r=\"3\" fill=\"#E65100\" />
<circle cx=\"700\" cy=\"493\" r=\"3\" fill=\"#E65100\" />

<!-- 装饰Emoji -->
{装饰Emoji}

<!-- 标题和副标题 -->
<text x=\"400\" y=\"30\" text-anchor=\"middle\" class=\"title\" fill=\"#E65100\">{主题}的一生</text>
<text x=\"400\" y=\"60\" text-anchor=\"middle\" class=\"subtitle\" fill=\"#EF6C00\">
<tspan x=\"400\" dy=\"0\">{副标题_第一行}</tspan>
<tspan x=\"400\" dy=\"20\">{副标题_第二行}</tspan>
</text>

<!-- 时间轴 -->
<line x1=\"50\" y1=\"420\" x2=\"750\" y2=\"420\" stroke=\"#E65100\" stroke-width=\"2\" />
{时间标签}

<!-- 生命阶段 -->
{生命阶段标签}

<!-- 数据点和科普信息 -->
{数据点和科普信息}

<!-- 曲线连接 -->
<path d=\"M50,270 Q140,280 230,290 T400,250 T580,210 T730,170\" fill=\"none\" stroke=\"#E65100\" stroke-width=\"2\"/>

<!-- 图例 -->
<rect x=\"50\" y=\"470\" width=\"700\" height=\"30\" fill=\"rgba(255,255,255,0.05)\"/>
<text x=\"60\" y=\"490\" class=\"legend-text\" fill=\"#E65100\">图例：</text>
<circle cx=\"150\" cy=\"485\" r=\"8\" fill=\"#E1BEE7\" stroke=\"#BA68C8\" stroke-width=\"1\"/>
<text x=\"170\" y=\"490\" class=\"legend-text\" fill=\"#E65100\">生命阶段</text>
<line x1=\"270\" y1=\"480\" x2=\"270\" y2=\"490\" stroke=\"#E65100\" stroke-width=\"2\"/>
<text x=\"290\" y=\"490\" class=\"legend-text\" fill=\"#E65100\">人生历程</text>
<text x=\"420\" y=\"490\" class=\"legend-text\" fill=\"#E65100\">{图例emoji}</text>
</svg>"))
(填充优化SVG模板 svg-template 主题 生命阶段 科普数据 背景样式 时间轴 阶段emoji 装饰emoji 副标题)))

(defun 填充优化SVG模板 (模板 主题 生命阶段 科普数据 背景样式 时间轴 阶段emoji 装饰emoji 副标题)
"填充SVG模板的具体实现"
(let ((填充模板 模板))
;; 实现填充逻辑
;; 例如：替换背景颜色、插入装饰Emoji、生成时间标签等
;; 特别注意：
;; 1. 装饰Emoji的精确位置
;; 2. 确保文字不会重叠或超出边界
;; 3. 圆圈使用不同颜色和淡色边框
;; 4. 科普信息文本布局优化，确保紧凑且易读
填充模板))

(defun 启动优化生成器 ()
(print "请输入您想了解的生命主题（如：牛马等）：")
(let ((用户输入 (read)))
(优化生命周期生成器 用户输入)))

;; 运行规则
;; 1. 启动时运行 (启动优化生成器) 函数
;; 2. 根据用户输入的主题，生成对应的生命周期SVG图表和描述
;; 3. 输出应包括优化后的SVG图表和相关的文字说明，重点突出科学数据和有趣事实
;; 4. 特别注意emoji的精确放置、文字布局的优化以及圆圈的设计