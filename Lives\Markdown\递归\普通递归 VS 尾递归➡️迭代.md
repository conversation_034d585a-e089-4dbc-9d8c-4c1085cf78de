让我用一个统一的例子来重新梳理这三种方式的区别。以"计算4的阶乘(4!)"为例：

## 1. 三种方式的本质区别

### 1.1 普通递归（等待式）
```

┌────────────────────────────────┐

│ 思维方式：                     │

│ 4! = 4 × (3!)                 │

│     必须等待3!计算完           │

│     3! = 3 × (2!)             │

│         必须等待2!计算完       │

│         2! = 2 × (1!)         │

│             必须等待1!计算完   │

│             1! = 1            │

│             开始返回结果       │

└────────────────────────────────┘

特点：

• 像多米诺骨牌，先倒下去再回来

• 每一层都要记住自己的任务

• 需要很多内存空间存储中间状态
```
### 1.2 尾递归（传递式）
```
┌────────────────────────────────┐

│ 思维方式：                     │

│ factorial(4, 1)                │

│   → factorial(3, 4×1)          │

│     → factorial(2, 3×4)        │

│       → factorial(1, 2×12)     │

│         → 24                   │

└────────────────────────────────┘

特点：

• 像接力跑，结果随跑随传

• 不需要记住之前的状态

• 只需要一个内存空间
```
### 1.3 迭代（循环式）
```
┌────────────────────────────────┐

│ 思维方式：                     │

│ result = 1                     │

│ result = 1 × 1 = 1            │

│ result = 1 × 2 = 2            │

│ result = 2 × 3 = 6            │

│ result = 6 × 4 = 24           │

└────────────────────────────────┘

特点：

• 像滚雪球，不断更新一个值

• 直接看到每一步结果

• 只需要一个内存空间
```
## 2. 生活中的类比

### 2.1 普通递归：套娃过程
```
打开最大的娃娃

  要先打开第二大的娃娃

    要先打开第三大的娃娃

      要先打开最小的娃娃

      找到了！开始往回组装
```
### 2.2 尾递归：传话游戏
```
A传给B："目前是1"

B传给C："目前是4"

C传给D："目前是12"

D得到最终结果："24"
```
### 2.3 迭代：计步器
```
从0开始

走1步 → 1步

再走1步 → 2步

再走1步 → 3步

再走1步 → 4步
```
## 3. 空间使用比较
```
┌──────────────┬────────────┬────────────┬────────────┐

│ 方式         │ 普通递归   │ 尾递归     │ 迭代       │

├──────────────┼────────────┼────────────┼────────────┤

│ 需要的空间   │ n个空间    │ 1个空间    │ 1个空间    │

│ 存储内容     │ 每层状态   │ 当前结果   │ 当前结果   │

└──────────────┴────────────┴────────────┴────────────┘
```
## 4. 关键区别

普通递归：
```
• 等待式，像嵌套的任务

• 需要记住每一层的状态

• 空间消耗大
```
尾递归：
```
• 传递式，像接力赛跑

• 只需要记住当前状态

• 空间消耗小
```
迭代：
```
• 更新式，像计步器

• 只需要记住当前值

• 空间消耗小

尾递归和迭代在本质上很相似，都只需要保存当前状态。区别在于表达方式：

- 尾递归用函数调用的方式表达

- 迭代用循环的方式表达
```
这就是为什么说"尾递归可以优化成迭代"，因为它们在本质上做着相同的事情。