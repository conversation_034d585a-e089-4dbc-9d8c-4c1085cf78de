<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内容一键转 HTML </title>
    <meta name="version" content="0.1.1">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Fira+Code&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <style>
        :root {
            --primary-color: #4a90e2;
            --secondary-color: #2c3e50;
            --background-color: #f5f6fa;
            --text-color: #2c3e50;
            --border-color: #e1e8ed;
            --hover-color: #357abd;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: var(--background-color);
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 95%;
            margin: 0 auto;
            padding: 1rem;
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .title-section {
            background: white;
            padding: 1.5rem 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px var(--shadow-color);
            width: calc(50% - 1rem);
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 140px;
        }

        .title-section h2 {
            color: #f39c12;
            margin: 0;
            font-weight: 700;
            font-size: 2.5rem;
            line-height: 1.3;
            text-align: center;
        }

        .link-input-section {
            background: white;
            padding: 1.5rem 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px var(--shadow-color);
            display: flex;
            flex-direction: column;
            gap: 1.2rem;
            width: calc(50% - 1rem);
        }

        .link-input-section h2 {
            color: var(--secondary-color);
            margin-bottom: 0;
            font-weight: 600;
            font-size: 1.4rem;
        }

        .input-container {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: nowrap;
        }

        .text-input {
            flex: 1;
            max-width: 500px;
            min-width: 200px;
            padding: 1rem 1.2rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 1.1rem;
            transition: border-color 0.3s ease;
        }

        .action-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1.1rem;
            transition: background-color 0.3s ease;
        }

        .action-button:hover {
            background: var(--hover-color);
        }

        .converter-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-top: 1rem;
            height: calc(100vh - 220px);
            min-height: 600px;
        }

        .editor-section, .preview-section {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px var(--shadow-color);
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .editor-section h2, .preview-section h2 {
            color: var(--secondary-color);
            margin-bottom: 1rem;
            font-weight: 600;
            font-size: 1.8rem;
        }

        #markdownInput {
            width: 100%;
            flex: 1;
            padding: 1.2rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-family: 'Fira Code', monospace;
            font-size: 1.1rem;
            line-height: 1.6;
            resize: none;
            height: 400px;
        }

        #markdownInput:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .preview-content {
            flex: 1;
            padding: 1.2rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            overflow-y: auto;
            font-size: 1.1rem;
            line-height: 1.6;
            height: 400px;
        }

        .preview-content h1, 
        .preview-content h2, 
        .preview-content h3, 
        .preview-content h4, 
        .preview-content h5, 
        .preview-content h6 {
            margin-top: 1.5em;
            margin-bottom: 0.5em;
            color: var(--secondary-color);
        }

        .preview-content p {
            margin-bottom: 1em;
        }

        .preview-content code {
            background: #f8f9fa;
            padding: 0.2em 0.4em;
            border-radius: 4px;
            font-family: 'Fira Code', monospace;
            font-size: 0.9em;
        }

        /* 按钮样式 */
        .action-button {
            background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 14px 24px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            min-width: 160px;
            text-align: center;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            letter-spacing: 0.5px;
        }

        .action-button svg {
            width: 20px;
            height: 20px;
        }

        .action-button:hover {
            background: linear-gradient(135deg, #357abd 0%, #2868a9 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .action-button:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* 预览区域按钮容器 */
        .preview-actions {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 20px;
        }

        .preview-actions h2 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }

        /* 输入区域按钮容器 */
        .input-actions {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }

            .container {
                width: 100%;
                max-width: 100%;
                padding: 0.5rem;
            }

            .header-container {
                flex-direction: column;
                gap: 1rem;
            }

            .title-section, .link-input-section {
                width: 100%;
            }

            .converter-container {
                grid-template-columns: 1fr;
                height: auto;
            }

            .editor-section, .preview-section {
                height: 400px;
            }
        }

        /* 加载动画样式 */
        .loading-container {
            display: none;
            margin-left: 10px;
        }

        .loading-container.active {
            display: block;
        }

        .loading-dots {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .loading-dots span {
            width: 8px;
            height: 8px;
            background-color: var(--primary-color);
            border-radius: 50%;
            display: inline-block;
            animation: bounce 0.5s infinite alternate;
        }

        .loading-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .loading-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes bounce {
            0% {
                transform: translateY(0);
            }
            100% {
                transform: translateY(-10px);
            }
        }

        /* 进度按钮样式 */
        .progress-button {
            position: relative;
            overflow: hidden;
        }

        .progress-button .progress {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 4px;
            background: rgba(255, 255, 255, 0.5);
            transition: width 0.3s ease;
        }

        .progress-button.loading .progress {
            width: 100%;
            transition: width 2s linear;
        }

        /* 版本号样式 */
        .version {
            font-size: 0.9rem;
            color: #666;
            font-weight: normal;
            margin-left: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header-container">
            <div class="title-section">
                <h2>内容一键转 HTML <span class="version"></span></h2>
            </div>
            <div class="link-input-section">
                <h2>请输入文章链接</h2>
                <div class="input-container">
                    <input type="text" id="linkInput" placeholder="输入文章链接..." class="text-input" style="max-width: 1100px;">
                    <button id="fetchContentButton" class="action-button progress-button">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                            <polyline points="17 8 12 3 7 8"/>
                            <line x1="12" y1="3" x2="12" y2="15"/>
                        </svg>
                        一键获取
                        <div class="progress"></div>
                    </button>
                    <div class="loading-container">
                        <div class="loading-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="converter-container">
            <div class="editor-section">
                <h2>Markdown Input</h2>
                <textarea id="markdownInput" placeholder="在这里输入或粘贴 Markdown 文本..."></textarea>
            </div>
            <div class="preview-section">
                <div class="preview-actions">
                    <h2 style="margin: 0;">HTML Preview</h2>
                    <button id="downloadHtmlButton" class="action-button">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                            <polyline points="7 10 12 15 17 10"/>
                            <line x1="12" y1="15" x2="12" y2="3"/>
                        </svg>
                        下载HTML
                    </button>
                    <button id="copyHtmlButton" class="action-button">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                        </svg>
                        复制HTML源码
                    </button>
                    <button id="previewHtmlButton" class="action-button">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                            <circle cx="12" cy="12" r="3"/>
                        </svg>
                        点我预览HTML
                    </button>
                    <button id="downloadPdfButton" class="action-button">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                            <polyline points="14 2 14 8 20 8"/>
                            <line x1="16" y1="13" x2="8" y2="13"/>
                            <line x1="16" y1="17" x2="8" y2="17"/>
                            <polyline points="10 12 9 12 4 17"/>
                        </svg>
                        下载PDF
                    </button>
                </div>
                <div id="htmlPreview" class="preview-content"></div>
            </div>
        </div>
    </div>

    <script id="htmlTemplate" type="text/template">
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>预览</title>
            <style>
                body {
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
                    line-height: 1.6;
                    color: #2c3e50;
                    background: #f5f6fa;
                    padding: 2rem;
                }
                h1, h2, h3, h4, h5, h6 {
                    margin-top: 1.5em;
                    margin-bottom: 0.5em;
                    color: #2c3e50;
                }
                p {
                    margin-bottom: 1em;
                }
                code {
                    background: #f8f9fa;
                    padding: 0.2em 0.4em;
                    border-radius: 4px;
                    font-family: 'Fira Code', monospace;
                    font-size: 0.9em;
                }
            </style>
        </head>
        <body>
            {{content}}
        </body>
        </html>
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 动态设置版本号
            const versionMeta = document.querySelector('meta[name="version"]');
            const versionSpan = document.querySelector('.version');
            if (versionMeta && versionSpan) {
                versionSpan.textContent = 'v' + versionMeta.getAttribute('content');
            }

            // 命名配置
            const CONFIG = {
                // 文件命名规则，可选参数：
                // - {title}: 文章标题
                // - {date}: 当前日期，格式：YYYYMMDD
                // - {time}: 当前时间，格式：HHmmss
                // 示例：
                // - "{title}" -> 使用文章标题
                // - "{title}_{date}" -> 标题_20231207
                // - "文章_{title}" -> 文章_标题
                // - "{date}_{title}" -> 20231207_标题
                namePattern: "{title}",
                
                // 获取格式化后的文件名
                getFormattedName: function(title) {
                    const now = new Date();
                    const date = now.getFullYear() +
                        String(now.getMonth() + 1).padStart(2, '0') +
                        String(now.getDate()).padStart(2, '0');
                    const time = String(now.getHours()).padStart(2, '0') +
                        String(now.getMinutes()).padStart(2, '0') +
                        String(now.getSeconds()).padStart(2, '0');
                    
                    // 替换模式中的变量
                    let name = this.namePattern
                        .replace('{title}', title)
                        .replace('{date}', date)
                        .replace('{time}', time);
                    
                    // 清理文件名（移除不允许的字符）
                    return name.replace(/[\\/:*?"<>|]/g, '_');
                }
            };

            const markdownInput = document.getElementById('markdownInput');
            const htmlPreview = document.getElementById('htmlPreview');
            const linkInput = document.getElementById('linkInput');
            const fetchContentButton = document.getElementById('fetchContentButton');
            const loadingContainer = document.querySelector('.loading-container');

            // Initialize marked with custom options
            marked.setOptions({
                gfm: true,
                breaks: true,
                headerIds: true,
                mangle: false,
                pedantic: false,
                headerPrefix: '',
                renderer: new marked.Renderer()
            });

            // Function to update preview
            function updatePreview() {
                try {
                    const markdownText = markdownInput.value;
                    
                    if (!markdownText) {
                        htmlPreview.innerHTML = '';
                        return;
                    }

                    // 在预览时处理内容
                    const processedContent = processMarkdownContent(markdownText);
                    const htmlContent = marked.parse(processedContent);
                    htmlPreview.innerHTML = htmlContent;
                } catch (error) {
                    console.error('Error converting markdown:', error);
                    htmlPreview.innerHTML = `<div class="error">Error converting markdown: ${error.message}</div>`;
                }
            }

            // Function to process markdown content
            function processMarkdownContent(content) {
                let processedContent = '';
                
                // 1. 提取标题和URL
                const titleMatch = content.match(/Title:(.*?)\n/);
                const urlMatch = content.match(/URL Source:(.*?)\n/);
                
                // 2. 如果有标题，添加为一级标题格式
                if (titleMatch) {
                    processedContent += `# ${titleMatch[1].trim()}\n\n`;
                }
                
                // 3. 如果有URL，添加为"阅读原文"链接
                if (urlMatch) {
                    const url = urlMatch[1].trim();
                    processedContent += `[阅读原文](${url})\n\n`;
                }
                
                // 4. 处理主要内容
                let mainContent = content;
                
                // 移除Title、URL Source和Markdown Content标记行
                mainContent = mainContent.replace(/Title:.*\n/, '');
                mainContent = mainContent.replace(/URL Source:.*\n/, '');
                mainContent = mainContent.replace(/Markdown Content:.*\n/, '');
                
                // 5. 处理图片URL，将微信图片转换为代理URL
                mainContent = mainContent.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, (match, alt, url) => {
                    if (url.includes('mmbiz.qpic.cn')) {
                        const cleanUrl = url.split('?')[0];
                        const proxyUrl = `https://images.weserv.nl/?url=${encodeURIComponent(cleanUrl)}`;
                        return `![${alt}](${proxyUrl})`;
                    }
                    return match;
                });
                
                // 6. 处理标题语法
                mainContent = mainContent.split('\n').map(line => {
                    // 确保#号后有空格
                    line = line.replace(/^(#{1,6})([^#\s])/gm, '$1 $2');
                    return line;
                }).join('\n');
                
                // 7. 添加处理后的主要内容
                processedContent += mainContent.trim();
                
                // 8. 清理多余的空行
                processedContent = processedContent.replace(/\n{3,}/g, '\n\n');

                // 9. 配置marked选项
                marked.setOptions({
                    renderer: new marked.Renderer(),
                    gfm: true,
                    breaks: true,
                    sanitize: false,
                    smartLists: true
                });

                // 10. 使用marked处理markdown
                try {
                    return marked.parse(processedContent);
                } catch (error) {
                    console.error('Markdown processing error:', error);
                    return '<p>Error processing markdown content.</p>';
                }
            }

            // Function to fetch content from URL
            async function fetchContent() {
                const url = linkInput.value.trim();
                if (!url) {
                    alert('请输入有效的URL地址');
                    return;
                }

                const loadingContainer = document.querySelector('.loading-container');
                const progressBar = fetchContentButton.querySelector('.progress');
                
                fetchContentButton.disabled = true;
                fetchContentButton.classList.add('loading');
                fetchContentButton.innerHTML = `加载中...<div class="progress"></div>`;
                loadingContainer.classList.add('active');
                
                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += 5;
                    if (progress <= 90) {
                        progressBar.style.width = `${progress}%`;
                    }
                }, 200);
                
                try {
                    const proxyUrl = `https://r.jina.ai/${url}`;
                    const response = await fetch(proxyUrl, {
                        headers: {
                            'X-Return-Format': 'markdown' // 指定返回格式为markdown
                        }
                    });
                    
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    
                    const content = await response.text();
                    markdownInput.value = content;
                    updatePreview();

                    progressBar.style.width = '100%';
                    
                } catch (error) {
                    console.error('Error fetching content:', error);
                    alert('获取内容失败，请检查URL地址是否正确，或稍后重试。');
                } finally {
                    clearInterval(progressInterval);
                    setTimeout(() => {
                        fetchContentButton.disabled = false;
                        fetchContentButton.classList.remove('loading');
                        fetchContentButton.innerHTML = `一键获取<div class="progress"></div>`;
                        loadingContainer.classList.remove('active');
                        progressBar.style.width = '0';
                    }, 300);
                }
            }

            // Function to get article title
            function getArticleTitle() {
                const titleMatch = markdownInput.value.match(/Title:(.*?)\n/);
                return titleMatch ? titleMatch[1].trim() : '预览';
            }

            // Add event listeners
            markdownInput.addEventListener('input', updatePreview);
            fetchContentButton.addEventListener('click', fetchContent);

            // Add download HTML functionality
            document.getElementById('downloadHtmlButton').addEventListener('click', function() {
                const previewContent = document.getElementById('htmlPreview').innerHTML;
                const template = document.getElementById('htmlTemplate').innerHTML;
                const title = getArticleTitle();
                
                // 替换模板中的标题和内容
                let formattedHTML = template.replace('{{content}}', previewContent);
                formattedHTML = formattedHTML.replace(/<title>预览<\/title>/, `<title>${title}</title>`);
                
                const fileName = CONFIG.getFormattedName(title);
                const blob = new Blob([formattedHTML], { type: 'text/html;charset=utf-8' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName + '.html';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            });

            // Add copy HTML source code functionality
            document.getElementById('copyHtmlButton').addEventListener('click', function() {
                const previewContent = document.getElementById('htmlPreview').innerHTML;
                const template = document.getElementById('htmlTemplate').innerHTML;
                const title = getArticleTitle();
                
                // 替换模板中的标题和内容
                let formattedHTML = template.replace('{{content}}', previewContent);
                formattedHTML = formattedHTML.replace(/<title>预览<\/title>/, `<title>${title}</title>`);
                
                navigator.clipboard.writeText(formattedHTML).then(function() {
                    const notification = document.createElement('div');
                    notification.className = 'notification';
                    notification.textContent = 'HTML源码已复制到剪贴板';
                    document.body.appendChild(notification);
                    
                    setTimeout(() => {
                        notification.remove();
                    }, 2000);
                }).catch(function(err) {
                    console.error('复制失败:', err);
                });
            });

            // Add preview HTML functionality
            document.getElementById('previewHtmlButton').addEventListener('click', function() {
                const previewContent = document.getElementById('htmlPreview').innerHTML;
                const template = document.getElementById('htmlTemplate').innerHTML;
                const title = getArticleTitle();
                
                // 替换模板中的标题和内容
                let formattedHTML = template.replace('{{content}}', previewContent);
                formattedHTML = formattedHTML.replace(/<title>预览<\/title>/, `<title>${title}</title>`);
                
                const previewWindow = window.open('', '_blank');
                if (previewWindow) {
                    previewWindow.document.write(formattedHTML);
                    previewWindow.document.close();
                }
            });

            // Add PDF download functionality
            document.getElementById('downloadPdfButton').addEventListener('click', function() {
                const previewContent = document.querySelector('.preview-content');
                if (!previewContent.innerHTML.trim()) {
                    alert('请先输入内容并预览后再下载 PDF');
                    return;
                }

                const title = getArticleTitle();
                const filename = CONFIG.getFormattedName(title) + '.pdf';
                
                const opt = {
                    margin: [10, 10],
                    filename: filename,
                    image: { type: 'jpeg', quality: 0.98 },
                    html2canvas: { 
                        scale: 2,
                        useCORS: true,
                        allowTaint: true,
                        scrollY: -window.scrollY
                    },
                    jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
                };

                html2pdf()
                    .from(previewContent)
                    .set(opt)
                    .save()
                    .catch(error => {
                        console.error('PDF generation error:', error);
                        alert('PDF生成失败，请重试');
                    });
            });

            // Add debounce to prevent too frequent updates
            let timeout;
            markdownInput.addEventListener('input', function() {
                clearTimeout(timeout);
                timeout = setTimeout(updatePreview, 300);
            });

            // Initialize with empty state
            markdownInput.value = '';
            updatePreview();
        });
    </script>
</body>
</html>
