"""
通用股票数据爬取与规范化工具

设计目标
- 以 scripts/yf_quant_three_stocks.py 为参考，抽象出可复用的数据获取与指标计算逻辑
- 支持多输入（文本、截图OCR、混合）解析股票代码
- 支持多数据源（当前实现 Yahoo Finance，留有扩展接口）
- 输出结构化、标准化数据，兼容量化/股票分析提示词

依赖
- 必需: pandas, numpy
- 数据源: yfinance (可选但推荐)
- OCR(可选): pillow, pytesseract

用法参见文件末尾的示例。
"""
from __future__ import annotations

import re
import math
import json
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Iterable, Any

import numpy as np
import pandas as pd

try:
    import yfinance as yf  # 数据源适配器: Yahoo Finance
except Exception:  # pragma: no cover
    yf = None

try:
    from PIL import Image  # OCR 读取图片
    import pytesseract
except Exception:  # pragma: no cover
    Image = None
    pytesseract = None

# 股票名称映射表（常用A股）
STOCK_NAMES = {
    '601869.SH': '长飞光纤',
    '600522.SH': '中天科技',
    '600487.SH': '亨通光电',
    '000001.SZ': '平安银行',
    '000002.SZ': '万科A',
    '600036.SH': '招商银行',
    '600519.SH': '贵州茅台',
    '000858.SZ': '五粮液',
    '002415.SZ': '海康威视',
    '000300.SH': '沪深300ETF',
    # 可根据需要扩展
}

# 行业分类映射
INDUSTRY_MAP = {
    '601869.SH': '通信设备',
    '600522.SH': '通信设备',
    '600487.SH': '通信设备',
    '600036.SH': '银行',
    '600519.SH': '白酒',
    '000858.SZ': '白酒',
    '002415.SZ': '安防设备',
    # 可根据需要扩展
}


# ------------------------------ 常量配置 ---------------------------------
RF_DEFAULT = 0.02          # 年化无风险利率 (用于夏普)
WIN_3M = 63                # ~3个月交易日窗口
PERIOD_DEFAULT = "9mo"     # 拉取足够历史用于3m/60d
INTERVAL_DEFAULT = "1d"


# ------------------------------ 代码规范化 ---------------------------------
class CodeNormalizer:
    """将用户输入的代码规范化为统一的“规范代码(canonical)”与“数据源代码(provider)”

    规范代码示例:
    - A股: 600522.SH / 000001.SZ
    - 港股: 0700.HK
    - 美股: TSLA, AAPL
    - 指数: 000300.SH, ^GSPC
    
    Yahoo Finance 对应规则:
    - A股上交所: 600522.SS, 深交所: 000001.SZ
    - 沪深300: 000300.SS
    - 港股: 0700.HK
    - 美股: 代码原样
    - 指数: ^GSPC 等原样
    """

    A_SH_RE = re.compile(r"^(?:SH)?\s*([6]\d{5})(?:\.SH)?$", re.IGNORECASE)
    A_SZ_RE = re.compile(r"^(?:SZ)?\s*([03]\d{5})(?:\.SZ)?$", re.IGNORECASE)
    A_FULL_RE = re.compile(r"^(\d{6})\.(SH|SZ)$", re.IGNORECASE)
    HK_RE = re.compile(r"^(?:HK)?\s*(\d{4,5})(?:\.HK)?$", re.IGNORECASE)
    US_RE = re.compile(r"^[A-Z]{1,6}$", re.IGNORECASE)

    @classmethod
    def to_canonical(cls, raw: str) -> Optional[str]:
        s = raw.strip().upper()
        if not s:
            return None
        # 指数保留
        if s.startswith("^"):
            return s
        # 已带 .SH/.SZ
        m = cls.A_FULL_RE.match(s)
        if m:
            code, ex = m.group(1), m.group(2)
            return f"{code}.{ex}"
        # 推断上/深
        m = cls.A_SH_RE.match(s)
        if m:
            return f"{m.group(1)}.SH"
        m = cls.A_SZ_RE.match(s)
        if m:
            return f"{m.group(1)}.SZ"
        # 港股
        m = cls.HK_RE.match(s)
        if m:
            code = m.group(1).zfill(4)
            return f"{code}.HK"
        # 美股
        if cls.US_RE.match(s):
            return s
        return None

    @staticmethod
    def to_yahoo_ticker(canonical: str) -> str:
        # A股上交所: .SH -> .SS；深交所: .SZ 不变
        if canonical.startswith("^"):
            return canonical  # 指数原样
        if canonical.endswith(".SH"):
            return canonical.replace(".SH", ".SS")
        return canonical


# ------------------------------ OCR 解析 ---------------------------------
class OCRCodeExtractor:
    """从股票信息截图中提取股票代码和实时信息。依赖 pillow + pytesseract，可选安装。

    允许匹配:
    - 6位A股代码, 可伴随 SH/SZ
    - 港股4-5位数字
    - 美股英文代码
    - 含有各种分隔符、括号、中文文本等

    新增功能：
    - 提取当前价格、涨跌幅
    - 提取成交量信息
    - 识别异常涨跌（>5%）
    """

    CODE_PATTERN = re.compile(
        r"(\^?[A-Za-z]{1,6}|\d{6}(?:\.(?:SH|SZ))?|\d{4,5}(?:\.HK)?)"
    )

    # 价格模式：匹配价格数字
    PRICE_PATTERN = re.compile(r"(\d+\.?\d*)")

    # 涨跌幅模式：匹配 +10.00% 或 -5.23% 格式
    CHANGE_PATTERN = re.compile(r"([+-]?\d+\.?\d*%)")

    def __init__(self, lang: str = "eng") -> None:
        self.lang = lang

    def extract_codes(self, image_path: str) -> List[str]:
        if Image is None or pytesseract is None:
            raise RuntimeError("需要 pillow 和 pytesseract 才能进行截图OCR解析。")
        img = Image.open(image_path)
        text = pytesseract.image_to_string(img, lang=self.lang)
        return self.extract_codes_from_text(text)

    def extract_full_info(self, image_path: str) -> Dict[str, Any]:
        """从截图中提取完整的股票信息，包括代码、价格、涨跌幅等"""
        if Image is None or pytesseract is None:
            raise RuntimeError("需要 pillow 和 pytesseract 才能进行截图OCR解析。")

        img = Image.open(image_path)
        text = pytesseract.image_to_string(img, lang=self.lang)

        # 提取股票代码
        codes = self.extract_codes_from_text(text)

        # 提取价格信息
        prices = self.PRICE_PATTERN.findall(text)

        # 提取涨跌幅信息
        changes = self.CHANGE_PATTERN.findall(text)

        return {
            'codes': codes,
            'prices': prices,
            'changes': changes,
            'raw_text': text,
            'anomaly_detected': any(abs(float(c.replace('%', '').replace('+', ''))) > 5
                                  for c in changes if c.replace('%', '').replace('+', '').replace('-', '').replace('.', '').isdigit())
        }

    @classmethod
    def extract_codes_from_text(cls, text: str) -> List[str]:
        if not text:
            return []
        found = cls.CODE_PATTERN.findall(text)
        # 过滤明显的非代码片段
        candidates = []
        for t in found:
            s = t.strip().upper()
            # 排除纯字母单字母噪声
            if re.fullmatch(r"[A-Z]", s):
                continue
            candidates.append(s)
        # 归一化
        canonicals = []
        for s in candidates:
            c = CodeNormalizer.to_canonical(s)
            if c:
                canonicals.append(c)
        # 去重
        out = []
        for c in canonicals:
            if c not in out:
                out.append(c)
        return out


# --------------------------- 数据源适配接口 -------------------------------
class DataSource:
    def fetch(self, canonical_codes: List[str], period: str, interval: str) -> Tuple[Dict[str, pd.DataFrame], List[str]]:
        """拉取多标的OHLCV数据
        返回: (frames, errors)
        - frames: {canonical_code: df}
          df列包含: open/high/low/close/volume
        - errors: 拉取失败的规范代码list
        """
        raise NotImplementedError


class YahooFinanceSource(DataSource):
    def __init__(self) -> None:
        if yf is None:
            raise RuntimeError("未安装 yfinance，无法使用 YahooFinance 数据源。请先 pip install yfinance")

    def fetch(self, canonical_codes: List[str], period: str, interval: str) -> Tuple[Dict[str, pd.DataFrame], List[str]]:
        if not canonical_codes:
            return {}, []
        mapping = {c: CodeNormalizer.to_yahoo_ticker(c) for c in canonical_codes}
        tickers = list(mapping.values())
        data = yf.download(tickers, period=period, interval=interval, auto_adjust=False, progress=False)

        frames: Dict[str, pd.DataFrame] = {}
        errors: List[str] = []

        def to_df(sym_provider: str) -> Optional[pd.DataFrame]:
            try:
                sub = {
                    'open': data['Open'][sym_provider],
                    'high': data['High'][sym_provider],
                    'low': data['Low'][sym_provider],
                    'close': data['Close'][sym_provider],
                    'volume': data['Volume'][sym_provider],
                }
                df = pd.DataFrame(sub).dropna()
                return df
            except Exception:
                return None

        for c, y in mapping.items():
            df = to_df(y)
            if df is None or df.empty:
                errors.append(c)
            else:
                df['ret'] = df['close'].pct_change()
                frames[c] = df
        return frames, errors


# ------------------------------ 指标计算 ---------------------------------

def rsi14(close: pd.Series) -> pd.Series:
    delta = close.diff()
    gain = delta.clip(lower=0).ewm(alpha=1/14, adjust=False).mean()
    loss = (-delta.clip(upper=0)).ewm(alpha=1/14, adjust=False).mean()
    rs = gain / loss.replace(0, np.nan)
    return 100 - 100 / (1 + rs)


def macd(close: pd.Series) -> Tuple[pd.Series, pd.Series, pd.Series]:
    ema12 = close.ewm(span=12, adjust=False).mean()
    ema26 = close.ewm(span=26, adjust=False).mean()
    macd_val = ema12 - ema26
    signal = macd_val.ewm(span=9, adjust=False).mean()
    hist = macd_val - signal
    return macd_val, signal, hist


def atr14(df: pd.DataFrame) -> float:
    prev_close = df['close'].shift(1)
    tr = pd.concat([
        (df['high'] - df['low']).abs(),
        (df['high'] - prev_close).abs(),
        (df['low'] - prev_close).abs()
    ], axis=1).max(axis=1)
    return float(tr.rolling(14).mean().iloc[-1])


def support_resistance(close: pd.Series, win_3m: int = WIN_3M) -> Dict[str, float]:
    recent = close.tail(win_3m)
    q10, q50, q90 = recent.quantile([0.10, 0.50, 0.90])
    swing_high = recent.rolling(5, center=True).max().iloc[-10:].max()
    swing_low = recent.rolling(5, center=True).min().iloc[-10:].min()
    return {
        'q10': float(q10),
        'median': float(q50),
        'q90': float(q90),
        'swing_high': float(swing_high),
        'swing_low': float(swing_low),
    }


def get_stock_name(code: str) -> str:
    """获取股票中文名称"""
    return STOCK_NAMES.get(code, code)

def get_industry(code: str) -> str:
    """获取股票行业分类"""
    return INDUSTRY_MAP.get(code, "未分类")

def calculate_technical_score(rsi: float, macd_hist: float, bb_pos: float) -> int:
    """基于技术指标计算技术面评分（1-5分）"""
    score = 3  # 基础分

    # RSI评分：30-70正常区间+1分，超买超卖-1分
    if 30 <= rsi <= 70:
        score += 1
    elif rsi > 80 or rsi < 20:
        score -= 1

    # MACD柱状图：正值+1分，负值-1分
    if macd_hist > 0:
        score += 1
    elif macd_hist < 0:
        score -= 1

    # 布林带位置：0.2-0.8正常+1分，超出范围-1分
    if 0.2 <= bb_pos <= 0.8:
        score += 1
    elif bb_pos > 1.0 or bb_pos < 0:
        score -= 1

    return max(1, min(5, score))

def calculate_fund_score(vol_chg: float, beta: float) -> int:
    """基于成交量和贝塔值计算资金面评分（1-5分）"""
    score = 3  # 基础分

    # 成交量变化：放量+1分，缩量-1分
    if vol_chg > 0.5:  # 放量50%以上
        score += 1
    elif vol_chg < -0.3:  # 缩量30%以上
        score -= 1

    # 贝塔值：0.8-1.2正常+1分，过高过低-1分
    if 0.8 <= beta <= 1.2:
        score += 1
    elif beta > 2.0 or beta < 0.5:
        score -= 1

    return max(1, min(5, score))

def calculate_target_price(current: float, support_resistance: dict, trend_strength: float) -> float:
    """基于支撑阻力和趋势强度计算目标价"""
    if not support_resistance:
        return current * 1.05  # 默认5%涨幅

    q90 = support_resistance.get('q90', current)
    swing_high = support_resistance.get('swing_high', current)

    # 根据趋势强度调整目标价
    if trend_strength > 1.0:  # 强势上涨
        return max(q90 * 1.1, swing_high * 1.05)
    elif trend_strength > 0.5:  # 温和上涨
        return max(q90, swing_high * 1.02)
    else:  # 震荡或下跌
        return min(q90 * 0.95, current * 1.02)

def compute_metrics(frames: Dict[str, pd.DataFrame],
                    benchmark: Optional[str] = None,
                    rf: float = RF_DEFAULT,
                    win_3m: int = WIN_3M) -> Dict[str, Dict[str, Any]]:
    """对给定标的集合计算一组与参考脚本兼容的指标。

    frames: {canonical: df(open/high/low/close/volume, ret)}
    benchmark: 规范代码，如 000300.SH 或 ^GSPC；若提供且存在，则计算指数相对指标
    返回: {canonical: metrics_dict}
    """
    # 指数用于相对指标
    idx_df: Optional[pd.DataFrame] = None
    if benchmark and benchmark in frames:
        idx_df = frames[benchmark].tail(win_3m)

    out: Dict[str, Dict[str, Any]] = {}
    for sym, df_full in frames.items():
        if sym == benchmark:
            continue
        if df_full is None or df_full.empty or 'ret' not in df_full:
            continue
        df = df_full.tail(win_3m).copy()

        mu = df['ret'].mean()
        sigma = df['ret'].std()
        ann_sigma = float(sigma * math.sqrt(252)) if not np.isnan(sigma) else None
        cumret = float((1 + df['ret'].dropna()).prod() - 1)
        mdd = float(((df['close']/df['close'].cummax())-1).min())
        sharpe = float(((mu * 252 - rf) / (sigma * math.sqrt(252)))) if sigma and sigma > 0 else None

        vol_mean = float(df['volume'].mean()) if df['volume'].size else None
        vol_last5 = float(df['volume'].tail(5).mean()) if df['volume'].size else None
        vol_chg = (vol_last5/vol_mean - 1) if vol_mean and vol_mean > 0 else None

        ema20 = df['close'].ewm(span=20, adjust=False).mean()
        ema50 = df['close'].ewm(span=50, adjust=False).mean()
        slope_ema20 = float((ema20.iloc[-1] - ema20.iloc[-5]) / 5)
        slope_ema50 = float((ema50.iloc[-1] - ema50.iloc[-5]) / 5)
        ma_state = 'golden' if ema20.iloc[-1] > ema50.iloc[-1] else 'death'

        rsi_last = float(rsi14(df['close']).iloc[-1])
        macd_val, signal, hist = macd(df['close'])
        macd_last = float(macd_val.iloc[-1])
        hist_last = float(hist.iloc[-1])

        mb = df['close'].rolling(20).mean()
        sd = df['close'].rolling(20).std()
        upper = mb + 2*sd
        lower = mb - 2*sd
        bb_pos = float((df['close'].iloc[-1] - lower.iloc[-1]) / (upper.iloc[-1] - lower.iloc[-1])) if not np.isnan(upper.iloc[-1]) else None

        atr_val = atr14(df)
        supres = support_resistance(df['close'], win_3m)

        # 相对指数
        beta = alpha = corr = ex_ret = None
        if idx_df is not None and 'ret' in df and 'ret' in idx_df:
            ri = df[['ret']].join(idx_df['ret'], how='inner', rsuffix='_idx').dropna()
            if len(ri) > 10 and np.var(ri['ret_idx']) > 0:
                X = ri['ret_idx'].values
                Y = ri['ret'].values
                beta = float(np.cov(X, Y)[0, 1] / np.var(X))
                alpha = float(Y.mean() * 252 - beta * X.mean() * 252)
                corr = float(np.corrcoef(X, Y)[0, 1])
                ex_ret = float((1 + Y).prod() / (1 + X).prod() - 1)

        # 未来20日区间（最近60日均值/波动率）
        base = df_full.copy()
        base['ret'] = base['close'].pct_change()
        recent = base.tail(60)
        mu60 = recent['ret'].mean()
        s60 = recent['ret'].std()
        exp20 = float(mu60 * 20) if not np.isnan(mu60) else None
        s20 = float(s60 * math.sqrt(20)) if not np.isnan(s60) else None
        rng68 = (exp20 - s20, exp20 + s20) if (exp20 is not None and s20 is not None) else None
        rng95 = (exp20 - 2*s20, exp20 + 2*s20) if (exp20 is not None and s20 is not None) else None

        # 计算评分和目标价
        tech_score = calculate_technical_score(rsi_last or 50, hist_last or 0, bb_pos or 0.5)
        fund_score = calculate_fund_score(vol_chg or 0, beta or 1.0)
        trend_strength = abs(slope_ema20 or 0) + abs(slope_ema50 or 0)
        target_price = calculate_target_price(float(df['close'].iloc[-1]), supres, trend_strength)

        out[sym] = {
            'last_close': float(df['close'].iloc[-1]),
            'cumret_3m': float(cumret),
            'mu_d': float(mu) if not np.isnan(mu) else None,
            'sigma_d': float(sigma) if not np.isnan(sigma) else None,
            'ann_sigma': ann_sigma,
            'mdd': mdd,
            'sharpe': sharpe,
            'vol_mean': vol_mean,
            'vol_last5': vol_last5,
            'vol_chg5_vs_mean': float(vol_chg) if vol_chg is not None else None,
            'ema20_slope_per_day': slope_ema20,
            'ema50_slope_per_day': slope_ema50,
            'ma_state': ma_state,
            'rsi14': rsi_last,
            'macd': macd_last,
            'macd_hist': hist_last,
            'bb_pos_0to1': bb_pos,
            'atr14': float(atr_val) if not np.isnan(atr_val) else None,
            'support_resistance': supres,
            'beta': beta,
            'alpha_ann': alpha,
            'corr_idx': corr,
            'excess_ret_vs_idx': ex_ret,
            'pred_exp20': exp20,
            'pred_68': rng68,
            'pred_95': rng95,
            # 新增字段
            'stock_name': get_stock_name(sym),
            'industry': get_industry(sym),
            'technical_score': tech_score,
            'fund_score': fund_score,
            'target_price': target_price,
        }
    return out


# ------------------------------ 工具主类 ---------------------------------
@dataclass
class FetchResult:
    frames: Dict[str, pd.DataFrame]
    metrics: Dict[str, Dict[str, Any]]
    errors: List[str]


class StockDataTool:
    """通用股票数据爬取工具类

    - parse_inputs: 从文本/图片/混合解析出规范代码
    - fetch: 通过指定数据源拉取数据
    - compute: 计算指标（兼容参考脚本）
    - to_prompt_payload: 转换为便于提示词使用的结构
    """

    def __init__(self,
                 source: Optional[DataSource] = None,
                 period: str = PERIOD_DEFAULT,
                 interval: str = INTERVAL_DEFAULT,
                 rf: float = RF_DEFAULT,
                 benchmark: Optional[str] = None) -> None:
        self.source = source or YahooFinanceSource()
        self.period = period
        self.interval = interval
        self.rf = rf
        self.benchmark = benchmark  # 推荐: A股用 000300.SH；美股用 ^GSPC

    # -------------------- 输入解析 --------------------
    def parse_inputs(self,
                     codes_text: Optional[str] = None,
                     image_paths: Optional[Iterable[str]] = None) -> List[str]:
        res: List[str] = []
        if codes_text:
            res.extend(OCRCodeExtractor.extract_codes_from_text(codes_text))
        if image_paths:
            ocr = OCRCodeExtractor()
            for p in image_paths:
                try:
                    res.extend(ocr.extract_codes(p))
                except Exception:
                    # 图片解析失败不阻塞流程
                    continue
        # 去重
        out: List[str] = []
        for c in res:
            if c not in out:
                out.append(c)
        return out

    # -------------------- 数据拉取与计算 --------------------
    def fetch(self, canonicals: List[str]) -> FetchResult:
        if not canonicals:
            return FetchResult(frames={}, metrics={}, errors=["未提供任何可解析的股票代码"])
        # 如果设置基准且未包含在列表中，自动补齐，以便计算相对指标
        codes = list(canonicals)
        if self.benchmark and self.benchmark not in codes:
            codes.append(self.benchmark)

        frames, errors = self.source.fetch(codes, self.period, self.interval)
        # 计算指标
        metrics = compute_metrics(frames, benchmark=self.benchmark, rf=self.rf, win_3m=WIN_3M)
        # 仅返回用户请求的标的指标（不包含基准）
        metrics = {k: v for k, v in metrics.items() if k in canonicals}
        # 保留frames也只保留用户标的
        frames = {k: v for k, v in frames.items() if k in canonicals}
        return FetchResult(frames=frames, metrics=metrics, errors=errors)

    # -------------------- 提示词兼容结构 --------------------
    def to_prompt_payload(self, result: FetchResult, mode: str = "quant") -> Dict[str, Any]:
        """将结果转为提示词易用的数据结构
        mode:
          - quant: 面向量化分析师提示词，提供统计与技术指标
          - analyst: 面向股票分析师提示词，突出趋势/支撑阻力/量价
        """
        payload: Dict[str, Any] = {
            'mode': mode,
            'period': self.period,
            'interval': self.interval,
            'as_of': None,
            'errors': result.errors,
            'stocks': []
        }
        # as_of 取任意df的最后日期
        as_of = None
        for df in result.frames.values():
            if not df.empty:
                as_of = df.index.max()
                break
        payload['as_of'] = as_of.isoformat() if as_of is not None and hasattr(as_of, 'isoformat') else str(as_of)

        for code, m in result.metrics.items():
            item: Dict[str, Any] = {
                'code': code,
                'last_close': m.get('last_close'),
                'cumret_3m': m.get('cumret_3m'),
                'ann_sigma': m.get('ann_sigma'),
                'sharpe': m.get('sharpe'),
                'ma_state': m.get('ma_state'),
                'rsi14': m.get('rsi14'),
                'macd_hist': m.get('macd_hist'),
                'bb_pos_0to1': m.get('bb_pos_0to1'),
                'atr14': m.get('atr14'),
                'support_resistance': m.get('support_resistance'),
                'volume': {
                    'mean': m.get('vol_mean'),
                    'last5': m.get('vol_last5'),
                    'chg5_vs_mean': m.get('vol_chg5_vs_mean')
                },
                'relative_to_benchmark': {
                    'beta': m.get('beta'),
                    'alpha_ann': m.get('alpha_ann'),
                    'corr_idx': m.get('corr_idx'),
                    'excess_ret_vs_idx': m.get('excess_ret_vs_idx'),
                },
                'forecast_20d': {
                    'exp': m.get('pred_exp20'),
                    'range68': m.get('pred_68'),
                    'range95': m.get('pred_95')
                }
            }
            if mode == 'analyst':
                # 为股票分析师突出趋势/位置/支撑阻力
                item = {
                    'code': code,
                    'stock_name': m.get('stock_name'),
                    'industry': m.get('industry'),
                    'last_close': m.get('last_close'),
                    'cumret_3m': m.get('cumret_3m'),
                    'trend': {
                        'ma_state': m.get('ma_state'),
                        'ema20_slope_per_day': m.get('ema20_slope_per_day'),
                        'ema50_slope_per_day': m.get('ema50_slope_per_day'),
                    },
                    'momentum': {
                        'rsi14': m.get('rsi14'),
                        'macd': m.get('macd'),
                        'macd_hist': m.get('macd_hist'),
                    },
                    'location': {
                        'bb_pos_0to1': m.get('bb_pos_0to1'),
                        'support_resistance': m.get('support_resistance'),
                    },
                    'risk': {
                        'mdd': m.get('mdd'),
                        'ann_sigma': m.get('ann_sigma'),
                        'atr14': m.get('atr14'),
                    },
                    'volume': {
                        'mean': m.get('vol_mean'),
                        'last5': m.get('vol_last5'),
                        'chg5_vs_mean': m.get('vol_chg5_vs_mean')
                    },
                    'relative_to_benchmark': {
                        'beta': m.get('beta'),
                        'alpha_ann': m.get('alpha_ann'),
                        'corr_idx': m.get('corr_idx'),
                    },
                    'scores': {
                        'technical_score': m.get('technical_score'),
                        'fund_score': m.get('fund_score'),
                        'target_price': m.get('target_price'),
                    }
                }
            payload['stocks'].append(item)
        return payload

    def format_analysis_report(self, result: FetchResult, mode: str = "quant") -> str:
        """生成格式化的分析报告，兼容量化分析师和股票分析师两种模式"""
        if not result.metrics:
            return "无有效数据可分析"

        if mode == "analyst":
            return self._format_analyst_report(result)
        else:
            return self._format_quant_report(result)

    def _format_quant_report(self, result: FetchResult) -> str:
        """量化分析师格式报告"""
        report = []
        report.append("# 股票量化分析报告")
        report.append(f"数据截止时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M')}")
        report.append("")

        # 批量摘要表格
        report.append("## 批量摘要")
        report.append("| 股票代码 | 股票名称 | 当前价格 | 3月收益 | 技术状态 | RSI | 预期收益(20日) | 主要风险 |")
        report.append("|---------|---------|---------|---------|---------|-----|-------------|---------|")

        for code, metrics in result.metrics.items():
            name = metrics.get('stock_name', code)
            price = f"{metrics.get('last_close', 0):.2f}"
            ret_3m = f"{metrics.get('cumret_3m', 0)*100:.1f}%"
            ma_state = "[金叉]" if metrics.get('ma_state') == 'golden' else "[死叉]"
            rsi = metrics.get('rsi14', 50)
            rsi_status = "[超买]" if rsi > 70 else "[中性]" if rsi > 30 else "[超卖]"
            exp_ret = f"{metrics.get('pred_exp20', 0)*100:.1f}%"

            # 风险评估
            risk = "高波动" if metrics.get('ann_sigma', 0) > 0.5 else "中等波动" if metrics.get('ann_sigma', 0) > 0.3 else "低波动"
            if rsi > 80:
                risk = "严重超买"

            report.append(f"| {code} | {name} | {price} | {ret_3m} | {ma_state} | {rsi_status}({rsi:.0f}) | {exp_ret} | {risk} |")

        report.append("")
        return "\n".join(report)

    def _format_analyst_report(self, result: FetchResult) -> str:
        """股票分析师格式报告"""
        report = []

        for code, metrics in result.metrics.items():
            name = metrics.get('stock_name', code)
            industry = metrics.get('industry', '未分类')

            report.append(f"[股票] {name}（{code}）- {industry}")
            report.append("=" * 40)
            report.append("")

            # 1. 三月回顾
            cumret_3m = metrics.get('cumret_3m', 0) * 100
            ma_state = metrics.get('ma_state', 'unknown')
            vol_chg = metrics.get('vol_chg5_vs_mean', 0)

            trend_desc = "强势上涨" if ma_state == 'golden' and cumret_3m > 20 else \
                        "温和上涨" if ma_state == 'golden' else \
                        "震荡下跌" if ma_state == 'death' else "横盘整理"

            vol_desc = "明显放量" if vol_chg > 0.5 else "适度放量" if vol_chg > 0.2 else \
                      "缩量" if vol_chg < -0.2 else "正常"

            report.append("1. 三月回顾（简明扼要）")
            report.append(f"   • 涨跌幅：{cumret_3m:.1f}%")
            report.append(f"   • 主要趋势：{trend_desc}")
            report.append(f"   • 关键事件：[需结合行业和近期新闻]")
            report.append(f"   • 成交量：{vol_desc}")
            report.append("")

            # 2. 技术指标
            rsi = metrics.get('rsi14', 50)
            bb_pos = metrics.get('bb_pos_0to1', 0.5)
            sr = metrics.get('support_resistance', {})

            position_desc = "超买区域" if rsi > 70 else "超卖区域" if rsi < 30 else "正常区域"
            if bb_pos > 1.0:
                position_desc += "，突破上轨"
            elif bb_pos < 0.0:
                position_desc += "，跌破下轨"

            report.append("2. 技术指标")
            report.append(f"   • 当前位置：{position_desc}")
            report.append(f"   • 均线状态：{'多头排列' if ma_state == 'golden' else '空头排列'}")
            report.append(f"   • 关键支撑：{sr.get('q10', 0):.2f}元")
            report.append(f"   • 关键压力：{sr.get('q90', 0):.2f}元")
            report.append("")

            # 3. 综合评分
            tech_score = metrics.get('technical_score', 3)
            fund_score = metrics.get('fund_score', 3)

            report.append("3. 综合评分")
            report.append(f"   • 技术面：{tech_score}分（自动计算）")
            report.append(f"   • 资金面：{fund_score}分（自动计算）")
            report.append(f"   • 消息面：待评估（需人工评估）")
            report.append("")

            # 4. 一月预测
            target_price = metrics.get('target_price', metrics.get('last_close', 0))
            pred_exp = metrics.get('pred_exp20', 0) * 100

            trend_judge = "看涨" if tech_score >= 4 and fund_score >= 4 else \
                         "看跌" if tech_score <= 2 and fund_score <= 2 else "震荡"

            prob_desc = "高概率" if (tech_score + fund_score) >= 8 else \
                       "中等概率" if (tech_score + fund_score) >= 6 else "低概率"

            operation = "可适量关注" if trend_judge == "看涨" else \
                       "建议观望" if trend_judge == "震荡" else "谨慎回避"

            report.append("4. 一月预测")
            report.append(f"   • 趋势判断：{trend_judge}")
            report.append(f"   • 目标价位：{target_price:.2f}元（算法计算）")
            report.append(f"   • 概率评估：{prob_desc}")
            report.append(f"   • 操作建议：{operation}")
            report.append("")

            # 5. 风险提示
            mdd = metrics.get('mdd', 0) * 100
            ann_sigma = metrics.get('ann_sigma', 0) * 100
            stop_loss = sr.get('swing_low', metrics.get('last_close', 0)) * 0.95

            report.append("5. 风险提示")
            report.append(f"   • 主要风险点：最大回撤{mdd:.1f}%，年化波动{ann_sigma:.1f}%")
            report.append(f"   • 止损位建议：{stop_loss:.2f}元")
            report.append("")
            report.append("=" * 40)
            report.append("")

        return "\n".join(report)

    def validate_realtime_data(self, ocr_info: dict, api_metrics: dict) -> dict:
        """验证OCR提取的实时数据与API数据的一致性"""
        validation = {
            'price_diff_pct': 0,
            'data_freshness': 'unknown',
            'anomaly_confirmed': False,
            'warnings': []
        }

        if not ocr_info.get('prices') or not api_metrics:
            validation['warnings'].append("缺少对比数据")
            return validation

        # 尝试匹配OCR价格与API价格
        try:
            ocr_prices = [float(p) for p in ocr_info['prices'] if p.replace('.', '').isdigit()]
            if ocr_prices and api_metrics:
                # 取最接近的价格进行对比
                api_price = list(api_metrics.values())[0].get('last_close', 0)
                closest_ocr_price = min(ocr_prices, key=lambda x: abs(x - api_price))

                price_diff = abs(closest_ocr_price - api_price) / api_price * 100
                validation['price_diff_pct'] = price_diff

                if price_diff < 1:
                    validation['data_freshness'] = 'very_fresh'
                elif price_diff < 3:
                    validation['data_freshness'] = 'fresh'
                else:
                    validation['data_freshness'] = 'stale'
                    validation['warnings'].append(f"价格差异较大: {price_diff:.1f}%")

        except Exception as e:
            validation['warnings'].append(f"价格验证失败: {str(e)}")

        # 验证异常涨跌
        if ocr_info.get('anomaly_detected'):
            validation['anomaly_confirmed'] = True
            validation['warnings'].append("检测到异常涨跌，建议重点分析")

        return validation

    def get_industry_peers(self, code: str, limit: int = 5) -> List[str]:
        """获取同行业股票列表"""
        industry = get_industry(code)
        peers = [k for k, v in INDUSTRY_MAP.items() if v == industry and k != code]
        return peers[:limit]

    def enhanced_parse_inputs(self,
                            codes_text: Optional[str] = None,
                            image_paths: Optional[Iterable[str]] = None) -> Tuple[List[str], dict]:
        """增强版输入解析，返回代码列表和OCR信息"""
        codes = self.parse_inputs(codes_text, image_paths)
        ocr_info = {}

        if image_paths:
            ocr = OCRCodeExtractor()
            for path in image_paths:
                try:
                    info = ocr.extract_full_info(path)
                    ocr_info.update(info)
                except Exception as e:
                    ocr_info['errors'] = ocr_info.get('errors', []) + [str(e)]

        return codes, ocr_info


# ------------------------------ 使用示例 ---------------------------------
EXAMPLE = r"""
# 1) 文本/图片解析股票代码
from scripts.stock_data_tool import StockDataTool

# A股: 600522, 深市: 000001, 港股: 0700.HK 或 700, 美股: TSLA, 指数: ^GSPC
text = "我想看看 600522, 601869.SH, TSLA, 700, ^GSPC 的数据"
images = ["./screenshots/sample1.png"]  # 可选，如无pytesseract可忽略

tool = StockDataTool(benchmark="000300.SH")  # A股相对沪深300
codes = tool.parse_inputs(codes_text=text, image_paths=None)

# 2) 拉取数据 + 计算指标
result = tool.fetch(codes)

# 3) 转换为提示词友好结构（量化/分析师两种模式）
quant_payload = tool.to_prompt_payload(result, mode="quant")
analyst_payload = tool.to_prompt_payload(result, mode="analyst")

# 4) 序列化输出
print(json.dumps(quant_payload, ensure_ascii=False, indent=2))
"""

if __name__ == "__main__":  # 简易CLI用法
    demo_text = "研究 600522, 601869.SH 与 TSLA 以及 0700.HK"
    tool = StockDataTool(benchmark="000300.SH")
    codes = tool.parse_inputs(codes_text=demo_text)
    res = tool.fetch(codes)
    payload = tool.to_prompt_payload(res, mode="quant")
    print(json.dumps(payload, ensure_ascii=False, indent=2))

