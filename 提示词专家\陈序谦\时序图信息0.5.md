-- 信息表
CREATE TABLE 信息 (
键 VARCHAR(50) PRIMARY KEY,
值 TEXT
);

INSERT INTO 元信息 (键, 值) VALUES
('作者', '陈序谦'),
('版本', '0.5'),
('模型', 'claude sonnet'),
('用途', '深度呈现一个想法时序图');

-- 创建时序图基本信息表
CREATE TABLE 时序图信息 (
id INT PRIMARY KEY,
标题 TEXT,
背景色 VARCHAR(7),
主题 TEXT
);

INSERT INTO 时序图信息 (id, 标题, 背景色, 主题) VALUES
(1, '%标题', '#1e1e1e', '%主题');

-- 创建参与者表
CREATE TABLE 参与者 (
id INT PRIMARY KEY,
名称 TEXT,
描述 TEXT,
x坐标 INT
);

INSERT INTO 参与者 (id, 名称, 描述, x坐标) VALUES
(1, '%名称', '%描述', 100),
(2, '%名称', '%描述', 300),
(3, '%名称', '%描述', 500),
(4, '%名称', '%描述', 700);

-- 创建消息流表
CREATE TABLE 消息流 (
id INT PRIMARY KEY,
发送者_id INT,
接收者_id INT,
消息内容 TEXT,
顺序 INT,
FOREIGN KEY (发送者_id) REFERENCES 参与者(id),
FOREIGN KEY (接收者_id) REFERENCES 参与者(id)
);

INSERT INTO 消息流 (id, 发送者_id, 接收者_id, 消息内容, 顺序) VALUES
(1, 1, 2, '%消息内容%', 1),
(2, 2, 1, '%消息内容%', 2),
(3, 1, 3, '%消息内容%', 3),
(4, 3, 1, '%消息内容%', 4),
(5, 1, 4, '%消息内容%', 5),
(6, 4, 1, '%消息内容%', 6);

-- 创建注释表
CREATE TABLE 注释 (
id INT PRIMARY KEY,
内容 TEXT,
类型 VARCHAR(20),
相关消息_id INT,
FOREIGN KEY (相关消息_id) REFERENCES 消息流(id)
);

INSERT INTO 注释 (id, 内容, 类型, 相关消息_id) VALUES
(1, '%内容', '总结', NULL);

-- 创建时序图生成指令表
CREATE TABLE 时序图生成指令 (
id INT PRIMARY KEY,
步骤 TEXT,
说明 TEXT
);

INSERT INTO 时序图生成指令 (id, 步骤, 说明) VALUES
(1, '定义主题和参与者', '确定时序图的核心主题，并列出所有相关的参与者'),
(2, '绘制时间线', '为每个参与者创建垂直的生命线，表示时间流'),
(3, '添加交互', '按时间顺序添加参与者之间的消息和动作'),
(4, '标注重要事件', '在关键点添加注释或说明'),
(5, '美化样式', '选择合适的颜色、字体和图形样式以增强可读性'),
(6, '添加总结', '在图表底部添加一个简洁的总结或结论');

CREATE TABLE 运行规则 (
  id INT PRIMARY KEY, 
  规则 TEXT
); 
INSERT INTO 运行规则 (id, 规则) VALUES
CALL 思维流程('请您输入__想法，我将呈现想法时序图', @结果); 
SELECT @结果;

--注：此代码框架旨在引导AI的思考和分析过程，而非实际执行 
--AI应理解并内化这一结构，用于生成深度的想法洞察和视觉呈现
--理解群体是中文语境，用中文回答